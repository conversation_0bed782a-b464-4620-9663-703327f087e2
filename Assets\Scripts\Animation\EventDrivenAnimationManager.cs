using System;
using System.Collections.Generic;
using UnityEngine;
using Animation.Core;
using Events;
using Module.Mono.Animancer.RealsticFemale;
using Mono;

namespace Animation.Core
{
    /// <summary>
    /// Event-driven animation manager that coordinates between modules using your existing EventManager
    /// instead of direct method calls, reducing coupling and improving maintainability
    /// </summary>
    public class EventDrivenAnimationManager : MonoBehaviour
    {
        #region Inspector Fields

        [Header("Animation Controller")]
        [SerializeField] private UnifiedAnimationController animationController;

        [Header("Event Configuration")]
        [SerializeField] private bool enableEventLogging = true;
        [SerializeField] private bool validateEvents = true;
        [SerializeField] private float eventThrottleTime = 0.016f; // ~60 FPS

        [Header("State Management")]
        [SerializeField] private StateManager stateManager;
        [SerializeField] private CharacterParameters characterParameters;

        #endregion

        #region Private Fields

        private readonly Dictionary<Type, float> lastEventTimes = new Dictionary<Type, float>();
        private readonly Dictionary<string, int> eventCounts = new Dictionary<string, int>();
        private bool isInitialized = false;
        private AnimationStateContext lastBroadcastContext;

        #endregion

        #region Properties

        /// <summary>
        /// Whether the manager is properly initialized
        /// </summary>
        public bool IsInitialized => isInitialized;

        /// <summary>
        /// Current event statistics
        /// </summary>
        public Dictionary<string, int> EventStatistics => new Dictionary<string, int>(eventCounts);

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeEventManager();
        }

        private void Start()
        {
            SubscribeToEvents();
            isInitialized = true;
        }

        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the event-driven animation manager
        /// </summary>
        private void InitializeEventManager()
        {
            // Find components if not assigned
            if (animationController == null)
                animationController = FindObjectOfType<UnifiedAnimationController>();

            if (stateManager == null)
                stateManager = FindObjectOfType<StateManager>();

            if (characterParameters == null)
                characterParameters = FindObjectOfType<CharacterParameters>();

            if (animationController == null)
            {
                Debug.LogError("[EventDrivenAnimationManager] No UnifiedAnimationController found!");
                return;
            }

            LogEvent("EventDrivenAnimationManager initialized");
        }

        /// <summary>
        /// Subscribe to all relevant animation events using your existing EventManager
        /// </summary>
        private void SubscribeToEvents()
        {
            // Subscribe to new animation events
            EventManager.Subscribe<OnAnimationChangeRequestEvent>(OnAnimationChangeRequest);
            EventManager.Subscribe<OnMovementStateChangedEvent>(OnMovementStateChanged);
            EventManager.Subscribe<OnWeaponStateChangedEvent>(OnWeaponStateChanged);
            EventManager.Subscribe<OnCombatModeChangedEvent>(OnCombatModeChanged);
            EventManager.Subscribe<OnAnimationSystemSwitchedEvent>(OnAnimationSystemSwitched);

            // Subscribe to existing game events
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnAimingOnTargetEvent>(OnAimingAtTarget);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnStoppedAiming);
            EventManager.Subscribe<OnChangeDirectionEvent>(OnDirectionChanged);
            EventManager.Subscribe<OnFootStepEvent>(OnFootStep);
            EventManager.Subscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);

            LogEvent("Subscribed to animation events");
        }

        /// <summary>
        /// Unsubscribe from all events to prevent memory leaks
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            // Unsubscribe from animation events
            EventManager.Unsubscribe<OnAnimationChangeRequestEvent>(OnAnimationChangeRequest);
            EventManager.Unsubscribe<OnMovementStateChangedEvent>(OnMovementStateChanged);
            EventManager.Unsubscribe<OnWeaponStateChangedEvent>(OnWeaponStateChanged);
            EventManager.Unsubscribe<OnCombatModeChangedEvent>(OnCombatModeChanged);
            EventManager.Unsubscribe<OnAnimationSystemSwitchedEvent>(OnAnimationSystemSwitched);

            // Unsubscribe from game events
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnAimingAtTarget);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnStoppedAiming);
            EventManager.Unsubscribe<OnChangeDirectionEvent>(OnDirectionChanged);
            EventManager.Unsubscribe<OnFootStepEvent>(OnFootStep);
            EventManager.Unsubscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);

            LogEvent("Unsubscribed from animation events");
        }

        #endregion

        #region Animation Event Handlers

        /// <summary>
        /// Handle animation change requests
        /// </summary>
        private void OnAnimationChangeRequest(OnAnimationChangeRequestEvent eventData)
        {
            if (!ShouldProcessEvent<OnAnimationChangeRequestEvent>()) return;

            LogEvent($"Processing animation change request: {eventData.MovementState}/{eventData.WeaponState}");

            // Create animation context from event data
            var context = CreateContextFromRequest(eventData);

            if (validateEvents && !AnimationStateContextUtility.IsValidContext(context, out string error))
            {
                Debug.LogWarning($"[EventDrivenAnimationManager] Invalid animation request: {error}");
                return;
            }

            if (animationController != null && animationController.IsInitialized)
            {
                animationController.SetAnimationContext(context);
                BroadcastContextChange(animationController.PreviousContext, animationController.CurrentContext);
            }

            IncrementEventCount(nameof(OnAnimationChangeRequestEvent));
        }

        /// <summary>
        /// Handle movement state changes
        /// </summary>
        private void OnMovementStateChanged(OnMovementStateChangedEvent eventData)
        {
            if (!ShouldProcessEvent<OnMovementStateChangedEvent>()) return;

            LogEvent($"Movement state changed: {eventData.PreviousState} → {eventData.NewState}");

            if (animationController != null && animationController.IsInitialized)
            {
                var currentContext = animationController.CurrentContext;
                currentContext.movementState = eventData.NewState;
                currentContext.inputMagnitude = eventData.InputMagnitude;
                currentContext.inputDirection = eventData.InputDirection;
                currentContext.isMoving = eventData.IsMoving;

                animationController.SetAnimationContext(currentContext);
                BroadcastContextChange(animationController.PreviousContext, currentContext);
            }

            IncrementEventCount(nameof(OnMovementStateChangedEvent));
        }

        /// <summary>
        /// Handle weapon state changes
        /// </summary>
        private void OnWeaponStateChanged(OnWeaponStateChangedEvent eventData)
        {
            if (!ShouldProcessEvent<OnWeaponStateChangedEvent>()) return;

            LogEvent($"Weapon state changed: {eventData.PreviousState} → {eventData.NewState}");

            if (animationController != null && animationController.IsInitialized)
            {
                var currentContext = animationController.CurrentContext;
                currentContext.weaponState = eventData.NewState;
                currentContext.weaponModuleState = eventData.WeaponModule;
                currentContext.isAiming = eventData.IsAiming;
                currentContext.isShooting = eventData.IsShooting;

                animationController.SetAnimationContext(currentContext);
                BroadcastContextChange(animationController.PreviousContext, currentContext);
            }

            IncrementEventCount(nameof(OnWeaponStateChangedEvent));
        }

        /// <summary>
        /// Handle combat mode changes
        /// </summary>
        private void OnCombatModeChanged(OnCombatModeChangedEvent eventData)
        {
            if (!ShouldProcessEvent<OnCombatModeChangedEvent>()) return;

            LogEvent($"Combat mode changed: {(eventData.IsInCombat ? "ENTER" : "EXIT")} combat");

            if (animationController != null && animationController.IsInitialized)
            {
                var currentContext = animationController.CurrentContext;
                currentContext.mainState = eventData.MainState;

                if (eventData.IsInCombat)
                {
                    currentContext.weaponState = WeaponSubState.Aiming;
                    currentContext.isAiming = true;
                    if (currentContext.weaponModuleState == WeaponSubModuleState.EmptyHand)
                    {
                        currentContext.weaponModuleState = WeaponSubModuleState.Pistol;
                    }
                }
                else
                {
                    currentContext.weaponState = WeaponSubState.Idle;
                    currentContext.isAiming = false;
                    currentContext.isShooting = false;
                }

                animationController.SetAnimationContext(currentContext);
                BroadcastContextChange(animationController.PreviousContext, currentContext);
            }

            IncrementEventCount(nameof(OnCombatModeChangedEvent));
        }

        /// <summary>
        /// Handle animation system switches
        /// </summary>
        private void OnAnimationSystemSwitched(OnAnimationSystemSwitchedEvent eventData)
        {
            LogEvent($"Animation system switched: {eventData.PreviousSystem} → {eventData.NewSystem}");

            if (animationController != null)
            {
                animationController.SetAnimationSystem(eventData.UseAnimancer);
            }

            IncrementEventCount(nameof(OnAnimationSystemSwitchedEvent));
        }

        #endregion

        #region Game Event Handlers

        /// <summary>
        /// Handle target detection events
        /// </summary>
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            var combatEvent = new OnCombatModeChangedEvent(true, MainState.Combat, eventData.TargetPosition, true);
            EventManager.Broadcast(combatEvent);
        }

        /// <summary>
        /// Handle target lost events
        /// </summary>
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            var combatEvent = new OnCombatModeChangedEvent(false, MainState.Normal, Vector3.zero, false);
            EventManager.Broadcast(combatEvent);
        }

        /// <summary>
        /// Handle aiming at target events
        /// </summary>
        private void OnAimingAtTarget(OnAimingOnTargetEvent eventData)
        {
            var weaponEvent = new OnWeaponStateChangedEvent(
                WeaponSubState.Idle, WeaponSubState.Aiming,
                WeaponSubModuleState.Pistol, true, false);
            EventManager.Broadcast(weaponEvent);
        }

        /// <summary>
        /// Handle stopped aiming events
        /// </summary>
        private void OnStoppedAiming(OnUnAimingTargetEvent eventData)
        {
            var weaponEvent = new OnWeaponStateChangedEvent(
                WeaponSubState.Aiming, WeaponSubState.Idle,
                WeaponSubModuleState.Pistol, false, false);
            EventManager.Broadcast(weaponEvent);
        }

        /// <summary>
        /// Handle direction change events
        /// </summary>
        private void OnDirectionChanged(OnChangeDirectionEvent eventData)
        {
            if (animationController != null && animationController.IsInitialized)
            {
                var currentContext = animationController.CurrentContext;
                currentContext.isChangeDirection = true;
                currentContext.wantsToRotateBehindTarget = !eventData.IsAimToNormal;

                animationController.SetAnimationContext(currentContext);
            }
        }

        /// <summary>
        /// Handle footstep events
        /// </summary>
        private void OnFootStep(OnFootStepEvent eventData)
        {
            if (animationController != null && animationController.IsInitialized)
            {
                var currentContext = animationController.CurrentContext;
                currentContext.isLeftFootstep = eventData.IsLeftFoot;

                animationController.SetAnimationContext(currentContext);
            }
        }

        /// <summary>
        /// Handle character animation finished events
        /// </summary>
        private void OnCharacterAnimationFinished(OnCharacterAnimationFinishedEvent eventData)
        {
            LogEvent($"Character animation finished: {eventData.MovementSubState} ({eventData.AnimationLength}s)");

            // Broadcast transition completed event
            var transitionEvent = new OnAnimationTransitionCompletedEvent(
                eventData.MovementSubState.ToString(), eventData.AnimationLength, false, true);
            EventManager.Broadcast(transitionEvent);

            IncrementEventCount(nameof(OnCharacterAnimationFinishedEvent));
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Create animation context from animation change request event
        /// </summary>
        private AnimationStateContext CreateContextFromRequest(OnAnimationChangeRequestEvent eventData)
        {
            var context = animationController != null ? animationController.CurrentContext : AnimationStateContext.Default;

            context.movementState = eventData.MovementState;
            context.weaponState = eventData.WeaponState;
            context.weaponModuleState = eventData.WeaponModule;
            context.isAiming = eventData.IsAiming;
            context.isShooting = eventData.IsShooting;
            context.inputMagnitude = eventData.InputMagnitude;
            context.inputDirection = eventData.InputDirection;
            context.isMoving = eventData.InputMagnitude > 0.1f;

            return context;
        }

        /// <summary>
        /// Broadcast context change if it's significantly different
        /// </summary>
        private void BroadcastContextChange(AnimationStateContext previous, AnimationStateContext current)
        {
            if (current.HasChanged(lastBroadcastContext))
            {
                // Broadcast movement state change if needed
                if (current.HasMovementChanged(previous))
                {
                    var movementEvent = new OnMovementStateChangedEvent(
                        previous.movementState, current.movementState,
                        current.inputMagnitude, current.inputDirection, current.isMoving);
                    EventManager.Broadcast(movementEvent);
                }

                // Broadcast weapon state change if needed
                if (current.HasWeaponChanged(previous))
                {
                    var weaponEvent = new OnWeaponStateChangedEvent(
                        previous.weaponState, current.weaponState,
                        current.weaponModuleState, current.isAiming, current.isShooting);
                    EventManager.Broadcast(weaponEvent);
                }

                // Broadcast transition started event
                if (current.HasCoreStateChanged(previous))
                {
                    var transitionEvent = new OnAnimationTransitionStartedEvent(
                        previous.movementState.ToString(), current.movementState.ToString(),
                        AnimationStateContextUtility.GetRecommendedTransitionDuration(previous, current));
                    EventManager.Broadcast(transitionEvent);
                }

                lastBroadcastContext = current;
            }
        }

        /// <summary>
        /// Check if an event should be processed based on throttling
        /// </summary>
        private bool ShouldProcessEvent<T>()
        {
            var eventType = typeof(T);
            var currentTime = Time.time;

            if (lastEventTimes.TryGetValue(eventType, out float lastTime))
            {
                if (currentTime - lastTime < eventThrottleTime)
                {
                    return false;
                }
            }

            lastEventTimes[eventType] = currentTime;
            return true;
        }

        /// <summary>
        /// Increment event count for statistics
        /// </summary>
        private void IncrementEventCount(string eventName)
        {
            if (eventCounts.ContainsKey(eventName))
            {
                eventCounts[eventName]++;
            }
            else
            {
                eventCounts[eventName] = 1;
            }
        }

        /// <summary>
        /// Log event if logging is enabled
        /// </summary>
        private void LogEvent(string message)
        {
            if (enableEventLogging)
            {
                Debug.Log($"[EventDrivenAnimationManager] {message}");
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Manually trigger an animation change request
        /// </summary>
        public void RequestAnimationChange(MovementSubState movement, WeaponSubState weapon,
            WeaponSubModuleState module = WeaponSubModuleState.EmptyHand, bool aiming = false, bool shooting = false,
            float magnitude = 0f, Vector3 direction = default, bool force = false, float duration = -1f)
        {
            var requestEvent = new OnAnimationChangeRequestEvent(movement, weapon, module, aiming, shooting,
                magnitude, direction, force, duration);
            EventManager.Broadcast(requestEvent);
        }

        /// <summary>
        /// Request movement state change
        /// </summary>
        public void RequestMovementChange(MovementSubState newState, float inputMagnitude = 0f,
            Vector3 inputDirection = default, bool isMoving = false)
        {
            var movementEvent = new OnMovementStateChangedEvent(
                animationController?.CurrentContext.movementState ?? MovementSubState.Standing,
                newState, inputMagnitude, inputDirection, isMoving);
            EventManager.Broadcast(movementEvent);
        }

        /// <summary>
        /// Request weapon state change
        /// </summary>
        public void RequestWeaponChange(WeaponSubState newState, WeaponSubModuleState module = WeaponSubModuleState.EmptyHand,
            bool aiming = false, bool shooting = false)
        {
            var weaponEvent = new OnWeaponStateChangedEvent(
                animationController?.CurrentContext.weaponState ?? WeaponSubState.Idle,
                newState, module, aiming, shooting);
            EventManager.Broadcast(weaponEvent);
        }

        /// <summary>
        /// Request combat mode change
        /// </summary>
        public void RequestCombatModeChange(bool enterCombat, Vector3 targetPosition = default, bool hasTarget = false)
        {
            var combatEvent = new OnCombatModeChangedEvent(enterCombat,
                enterCombat ? MainState.Combat : MainState.Normal, targetPosition, hasTarget);
            EventManager.Broadcast(combatEvent);
        }

        /// <summary>
        /// Request animation system switch
        /// </summary>
        public void RequestAnimationSystemSwitch(bool useAnimancer)
        {
            var systemEvent = new OnAnimationSystemSwitchedEvent(useAnimancer);
            EventManager.Broadcast(systemEvent);
        }

        /// <summary>
        /// Get current event statistics
        /// </summary>
        public string GetEventStatistics()
        {
            var stats = new System.Text.StringBuilder();
            stats.AppendLine("Event Statistics:");

            foreach (var kvp in eventCounts)
            {
                stats.AppendLine($"  {kvp.Key}: {kvp.Value}");
            }

            return stats.ToString();
        }

        /// <summary>
        /// Get current animation state as string
        /// </summary>
        public string GetCurrentAnimationState()
        {
            if (animationController != null && animationController.IsInitialized)
            {
                return animationController.GetCurrentAnimationInfo();
            }
            return "Animation controller not available";
        }

        #endregion
    }
}