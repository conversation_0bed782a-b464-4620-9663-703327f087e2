using System;
using System.Collections.Generic;
using Events;
using Module.Mono.Animancer.RealsticFemale;
using MonoToECSShadow.Components;
using PlayerFAP.Components.Weapon;
using PlayerFAP.ScriptableObjects;
using PlayerFAP.Systems.Weapon;
using Sirenix.OdinInspector;
using Sirenix.Utilities;
using SubModule.Weapon;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using Unity.Collections;
using Unity.Scenes;
using UnityEngine;
using UnityEngine.Serialization;
using Random = Unity.Mathematics.Random;

namespace DefaultNamespace.Mono.Interface
{
    public class WeaponModule : SerializedMonoBehaviour, IWeaponModule,
        INeedSubModule<WeaponSubModuleState, SubModule.Weapon.WeaponSubModule>,
        IUpdateState<WeaponSubState>, INeedInput<CharacterParameter>
    {
        public bool CanUpdate { get; private set; }
        [field: SerializeField] public int ControllerIndex { get; private set; }
        [SerializeField] private int m_weaponIndex = -1;
        [field: SerializeField] public List<MainState> MainState { get; private set; }

        [field: SerializeField]
        public Dictionary<WeaponSubModuleState, SubModule.Weapon.WeaponSubModule> SubModules { get; private set; }

        [SerializeField] private WeaponSubModuleState _subModuleState;

        [field: SerializeField] public Vector3 GetCurrentAimPointer => m_CurrentSubModuleState.AimPointer.position;

        public WeaponSubModuleState SubModuleState
        {
            get { return _subModuleState; }
            set
            {
                _subModuleState = value;
                PlayerController.Instance.StateManager.CurrentSubState = _subModuleState;
            }
        }

        public void OnSubModuleChange(WeaponSubModuleState subModuleState)
        {
        }

        [field: SerializeField] public bool HasRefreshRate { get; private set; }
        [field: SerializeField] public float RefreshRate { get; private set; }
        public Enum GetModuleSubState() => SubModuleState;
        public int currentWeaponLevel { get; private set; }
        [SerializeField] private WeaponAnimationModule animationModule;

        [SerializeField] private WeaponSubModule m_CurrentSubModuleState;
        [field: SerializeField] public List<CharacterParameter> InputName { get; private set; }

        [SerializeField] private WeaponSubState _weaponSubState;

        [SerializeField] private bool m_isShooting;
        [SerializeField] private bool m_isAiming;
        [SerializeField] private bool m_autoShooting = false;
        [SerializeField] private bool m_lastShootingState = false;
        [SerializeField] private bool m_detectedTarget = false;
        [field: SerializeField] public BulletType CurrentBulletType { get; set; }

        [Header("Bullet Settings")] public GameObject BulletPrefab;
        [SerializeField] public List<BulletPrefabData> BulletPrefabs;
        [SerializeField] private BulletTypeData bulletTypeData;
        [SerializeField] private float bulletSpeed = 20f;
        [SerializeField] private float spreadMultiplier = 1f;

        private EntityManager entityManager;
        private EntityQuery weaponQuery;
        private Entity weaponEntity;
        private Random random;
        private uint seed;
        

        public void Initialize(int controllerIndex)
        {
            currentWeaponLevel = 1;
            entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
            random = Random.CreateFromIndex(1);
            seed = 1;

            // Find the weapon entity that was baked
            weaponQuery = entityManager.CreateEntityQuery(typeof(ShootingComponent), typeof(WeaponStateComponent));

            // Try to find the weapon entity immediately
            TryFindWeaponEntity();

            // If we couldn't find it, log a warning but don't fail - we'll try again later
            if (weaponEntity == Entity.Null || !entityManager.Exists(weaponEntity))
            {
                Debug.LogWarning("Weapon entity not found during initialization. Will try again when equipping weapon.");
            }
            
            animationModule = GetComponent<WeaponAnimationModule>();
            animationModule.Initialize(true);
        }

        private bool TryFindWeaponEntity()
        {
            if (weaponQuery == null || entityManager == null || !World.DefaultGameObjectInjectionWorld.IsCreated)
                return false;

            if (!weaponQuery.IsEmpty)
            {
                try
                {
                    weaponEntity = weaponQuery.GetSingletonEntity();
                    return entityManager.Exists(weaponEntity);
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"Error finding weapon entity: {e.Message}");
                    return false;
                }
            }
            return false;
        }

        private Entity GetBulletPrefabEntity(BulletType bulletType)
        {
            // If weapon entity doesn't exist or is invalid, try to find it again
            if (weaponEntity == Entity.Null || !entityManager.Exists(weaponEntity))
            {
                // Try to find the weapon entity
                bool found = TryFindWeaponEntity();

                // If still not found, log error and return null
                if (!found)
                {
                    Debug.LogError("Weapon entity doesn't exist! Trying to find it failed.");
                    return Entity.Null;
                }
            }

            try
            {
                var prefabBuffer = entityManager.GetBuffer<BulletPrefab>(weaponEntity);
                for (int i = 0; i < prefabBuffer.Length; i++)
                {
                    if (prefabBuffer[i].BulletType == bulletType)
                    {
                        return prefabBuffer[i].PrefabEntity;
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error accessing bullet prefab buffer: {e.Message}");
                return Entity.Null;
            }

            return Entity.Null;
        }

        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
           
            if (m_weaponIndex == -1)
                return;

            if (m_CurrentSubModuleState != null && m_CurrentSubModuleState != SubModules[SubModuleState])
            {
                EquipWeapon(SubModuleState);
            }

            // Debug shooting state
            if (m_isShooting && SubModules[SubModuleState].CanShoot)
            {
                // Check if weapon entity exists before shooting
                if (weaponEntity == Entity.Null || !entityManager.Exists(weaponEntity))
                {
                    // Try to find the weapon entity
                    bool found = TryFindWeaponEntity();

                    // If still not found, log warning and skip shooting this frame
                    if (!found)
                    {
                        Debug.LogWarning("Cannot shoot - weapon entity not found!");
                        return;
                    }
                }

                var currentSubModule = SubModules[SubModuleState];

                // Calculate total spread using accuracy and recoil
                float baseSpread = currentSubModule.SpreadAngle * currentSubModule.CurrentRecoil;
                float accuracySpread = (1 - currentSubModule.Accuracy) * currentSubModule.SpreadAngle;
                float totalSpread = baseSpread + accuracySpread;

                // Generate random spread values
                float horizontalSpread = random.NextFloat(-totalSpread, totalSpread) * math.PI / 180f;
                float verticalSpread = random.NextFloat(-totalSpread, totalSpread) * math.PI / 180f;

                // Get aiming vectors
                var aimPosition = GetCurrentAimPointer;
                var baseForward = math.normalize(currentSubModule.AimPointer.forward);

                // Apply compound spread rotation
                var finalRotation = math.mul(
                    quaternion.RotateY(horizontalSpread),
                    quaternion.RotateX(verticalSpread)
                );
                var shootingDir = math.rotate(finalRotation, baseForward);

                // Get bullet prefab using weapon's range limitations
                var bulletPrefab = GetBulletPrefabEntity(CurrentBulletType);
                if (bulletPrefab != Entity.Null)
                {
                    try
                    {
                        // Create and configure bullet entity
                        var bullet = entityManager.Instantiate(bulletPrefab);

                        // Configure bullet physics
                        var bulletTransform = new LocalTransform
                        {
                            Position = aimPosition,
                            Rotation = quaternion.LookRotation(shootingDir, math.up()),
                            Scale = 1f
                        };

                        // Set bullet behavior parameters
                        var bulletComponent = entityManager.GetComponentData<BulletComponent>(bullet);
                        bulletComponent.InitialPosition = aimPosition;
                        bulletComponent.Direction = shootingDir;
                        bulletComponent.MaxDistance = currentSubModule.MaxRange;
                        bulletComponent.Damage = currentSubModule.DamageAmount;
                        bulletComponent.TraveledDistance = 0f;
                        bulletComponent.Shooter = weaponEntity;

                        // Get bullet speed from WeaponUpgradeManager if available
                        if (WeaponUpgradeManager.Instance != null)
                        {
                            var bulletStats = WeaponUpgradeManager.Instance.GetCurrentBulletStats(CurrentBulletType);
                            if (bulletStats != null)
                            {
                                bulletComponent.Speed = bulletStats.speed;
                                DebugLogManager.Instance.Log($"Setting bullet speed from config: {bulletStats.speed}");
                            }
                            else
                            {
                                Debug.LogWarning($"No bullet stats found for type {CurrentBulletType}, using default speed");
                            }
                        }

                        entityManager.SetComponentData(bullet, bulletTransform);
                        entityManager.SetComponentData(bullet, bulletComponent);

                        // Update weapon state
                        currentSubModule.LastShotTime = Time.time;
                        currentSubModule.CurrentRecoil = math.clamp(
                            currentSubModule.CurrentRecoil + currentSubModule.RecoilAmount,
                            0f,
                            1f
                        );

                        if (currentSubModule.DamagePerShoot > 0)
                        {
                            // Apply accuracy-based durability loss
                            float durabilityLoss = currentSubModule.DamagePerShoot *
                                                   (1 - currentSubModule.Accuracy);
                            currentSubModule.TakeDamage((int) durabilityLoss);
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Bullet instantiation failed: {e.Message}");
                    }
                }

                // Call Shooting method which will update NextFireTime internally
                currentSubModule.Shooting(CurrentBulletType);

                // Debug the updated NextFireTime
                float timeInterval = (currentSubModule.FireRate > 0) ? 1f / currentSubModule.FireRate : 0.1f;
                // DebugLogManager.Instance.Log($"After Shooting: NextFireTime={currentSubModule.NextFireTime}, Time={Time.time}, " +
                //           $"FireRate={currentSubModule.FireRate} shots/sec, Time Interval={timeInterval} sec, " +
                //           $"Delay until next shot={currentSubModule.NextFireTime - Time.time} sec");
            }
        }

        public void UpdateSubModule(WeaponSubModuleState subModuleState)
        {
            switch (subModuleState)
            {
                case WeaponSubModuleState.Pistol:

                    break;
                case WeaponSubModuleState.Mp4:

                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(subModuleState), subModuleState, null);
            }
        }

        private void EquipWeapon(WeaponSubModuleState weaponSubModuleState)
        {
            DebugLogManager.Instance.Log("Equipping weapon: " + SubModules[weaponSubModuleState].name);
            if (m_CurrentSubModuleState != null)
            {
                SubState = WeaponSubState.UnEquipping;
                SetState();
            }

            m_CurrentSubModuleState = SubModules[weaponSubModuleState];
            SubState = WeaponSubState.Equipping;
            SetState();

            // Get the weapon entity when equipping
            try
            {
                var localWeaponQuery = World.DefaultGameObjectInjectionWorld.EntityManager
                    .CreateEntityQuery(typeof(ShootingComponent), typeof(WeaponStateComponent));

                if (!localWeaponQuery.IsEmpty)
                {
                    weaponEntity = localWeaponQuery.GetSingletonEntity();
                    DebugLogManager.Instance.Log($"Weapon entity found during equip: {weaponEntity.Index}");
                }
                else
                {
                    Debug.LogWarning("No weapon entities found with ShootingComponent and WeaponStateComponent.");
                    // Try with just ShootingComponent as a fallback
                    localWeaponQuery = World.DefaultGameObjectInjectionWorld.EntityManager
                        .CreateEntityQuery(typeof(ShootingComponent));

                    if (!localWeaponQuery.IsEmpty)
                    {
                        weaponEntity = localWeaponQuery.GetSingletonEntity();
                        DebugLogManager.Instance.Log($"Weapon entity found with only ShootingComponent: {weaponEntity.Index}");
                    }
                    else
                    {
                        Debug.LogError("No weapon entities found at all. This will cause errors when shooting.");
                    }
                }

                // Update weapon properties from WeaponUpgradeManager
                UpdateWeaponPropertiesFromConfig(weaponSubModuleState);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error finding weapon entity during equip: {e.Message}");
            }
        }

        // Update weapon properties from WeaponUpgradeManager
        private void UpdateWeaponPropertiesFromConfig(WeaponSubModuleState weaponSubModuleState)
        {
            if (WeaponUpgradeManager.Instance == null)
            {
                Debug.LogWarning("WeaponUpgradeManager not found, cannot update weapon properties from config");
                return;
            }

            var currentSubModule = SubModules[weaponSubModuleState];
            if (currentSubModule == null)
            {
                Debug.LogError($"SubModule for {weaponSubModuleState} not found");
                return;
            }

            // Get weapon stats from WeaponUpgradeManager
            var weaponStats = WeaponUpgradeManager.Instance.GetCurrentWeaponStats(weaponSubModuleState);
            if (weaponStats == null)
            {
                Debug.LogWarning($"No weapon stats found for {weaponSubModuleState}");
                return;
            }

            // Log current values before update
            DebugLogManager.Instance.Log($"Updating weapon properties for {weaponSubModuleState} from config:");
            DebugLogManager.Instance.Log($"  Before - FireRate: {currentSubModule.FireRate}, Accuracy: {currentSubModule.Accuracy}, " +
                      $"MaxRange: {currentSubModule.MaxRange}, DamageAmount: {currentSubModule.DamageAmount}");

            // Update weapon properties from config
            currentSubModule.FireRate = weaponStats.fireRate;
            currentSubModule.Accuracy = weaponStats.accuracy;
            currentSubModule.MaxRange = weaponStats.maxRange;
            currentSubModule.DamageAmount = weaponStats.damageAmount;
            currentSubModule.SpreadAngle = weaponStats.spreadAngle;
            currentSubModule.RecoilAmount = weaponStats.recoilAmount;
            currentSubModule.RecoilRecovery = weaponStats.recoilRecovery;

            // Log updated values
            DebugLogManager.Instance.Log($"  After - FireRate: {currentSubModule.FireRate}, Accuracy: {currentSubModule.Accuracy}, " +
                      $"MaxRange: {currentSubModule.MaxRange}, DamageAmount: {currentSubModule.DamageAmount}");

            // Also update the ECS entity if it exists
            if (weaponEntity != Entity.Null && entityManager != null && entityManager.Exists(weaponEntity))
            {
                if (entityManager.HasComponent<ShootingComponent>(weaponEntity))
                {
                    var shootingComponent = entityManager.GetComponentData<ShootingComponent>(weaponEntity);

                    // Log current values before update
                    DebugLogManager.Instance.Log($"  ECS Entity Before - FireRate: {shootingComponent.FireRate}, Accuracy: {shootingComponent.Accuracy}, " +
                              $"MaxRange: {shootingComponent.MaxRange}, DamageAmount: {shootingComponent.DamageAmount}");

                    // Update shooting component
                    shootingComponent.FireRate = weaponStats.fireRate;
                    shootingComponent.Accuracy = weaponStats.accuracy;
                    shootingComponent.MaxRange = weaponStats.maxRange;
                    shootingComponent.DamageAmount = weaponStats.damageAmount;
                    shootingComponent.SpreadAngle = weaponStats.spreadAngle;
                    shootingComponent.RecoilAmount = weaponStats.recoilAmount;
                    shootingComponent.RecoilRecovery = weaponStats.recoilRecovery;

                    entityManager.SetComponentData(weaponEntity, shootingComponent);

                    // Log updated values
                    DebugLogManager.Instance.Log($"  ECS Entity After - FireRate: {shootingComponent.FireRate}, Accuracy: {shootingComponent.Accuracy}, " +
                              $"MaxRange: {shootingComponent.MaxRange}, DamageAmount: {shootingComponent.DamageAmount}");
                }
            }
        }

        public Type GetSubModuleState()
        {
            return SubModuleState.GetType();
        }

        public Type GetModuleState()
        {
            return SubState.GetType();
        }

        public void HandleInput(Dictionary<CharacterParameter, object> inputValue)
        {
            // Handle weapon index changes
            if (inputValue.TryGetValue(CharacterParameter.WeaponIndex, out var weaponIndexValue))
            {
                if (m_weaponIndex != ((int) weaponIndexValue))
                {
                    m_weaponIndex = ((int) weaponIndexValue);
                    SubModuleState = (WeaponSubModuleState) m_weaponIndex;
                }

                if (m_weaponIndex == -1)
                {
                    return;
                }

                if (m_CurrentSubModuleState == null)
                    EquipWeapon(SubModuleState);
            }
            
            // Track aim status
            if (inputValue.TryGetValue(CharacterParameter.IsAiming, out var isAimingValue))
            {
                m_isAiming = (bool)isAimingValue;
            }
            
            // Track auto-shooting setting
            if (inputValue.TryGetValue(CharacterParameter.AutoShooting, out var autoShootingValue))
            {
                m_autoShooting = (bool)autoShootingValue;
            }
            
            if (inputValue.TryGetValue(CharacterParameter.DetectTarget, out var detectTarget))
            {
                m_detectedTarget = (bool)detectTarget;
            }
            
            // Handle direct shooting inputs
            bool manualShootingInput = false;
            if (inputValue.TryGetValue(CharacterParameter.IsShooting, out var isShootingValue))
            {
                manualShootingInput = (bool)isShootingValue;
            }

            if (m_isAiming && m_detectedTarget && (m_autoShooting || manualShootingInput))
            {
                SubState = WeaponSubState.Shooting;
                m_isShooting = true;
            }
            else if(m_isAiming)
            {
                SubState = WeaponSubState.Aiming;
                m_isShooting = false;
            }
            else
            {
                SubState = WeaponSubState.Idle;
                m_isShooting = false;
            }
            
            SetState();
                
            // Update the ECS component for shooting
            UpdatePlayerShootingInputData(m_isShooting);
        }
        
        // Update the PlayerShootingInputData ECS component
        private void UpdatePlayerShootingInputData(bool isShooting)
        {
            if (isShooting == m_lastShootingState)
                return;
                
            var entityQuery = World.DefaultGameObjectInjectionWorld.EntityManager.CreateEntityQuery(typeof(PlayerShootingInputData));
            if (entityQuery.CalculateEntityCount() > 0)
            {
                var playerEntity = entityQuery.GetSingletonEntity();
                if (World.DefaultGameObjectInjectionWorld.EntityManager.HasComponent<PlayerShootingInputData>(playerEntity))
                {
                    DebugLogManager.Instance.Log($"Updating PlayerShootingInputData: IsShooting={isShooting}");
                    
                    World.DefaultGameObjectInjectionWorld.EntityManager.SetComponentData(playerEntity,
                        new PlayerShootingInputData
                        {
                            IsShooting = isShooting,
                            CoolDownTime = 0.1f, // Default cooldown time, adjust as needed
                            LastShotTime = Time.time
                        });
                        
                    m_lastShootingState = isShooting;
                }
            }
        }

        public WeaponSubState SubState
        {
            get => _weaponSubState;
            set => _weaponSubState = value;
        }

        public void SetState()
        {
            PlayerController.Instance.StateManager.CurrentSubState = _weaponSubState;
        }

        private void OnDestroy()
        {
        }
    }
}