using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Animation.Core;
using PlayerFAP.ScriptableObjects;
using PlayerFAP.Managers;
using Module.Mono.Animancer.RealsticFemale;
using Events;

namespace Animation.Core
{
    /// <summary>
    /// Centralized manager for handling all animation transitions
    /// Uses the configuration system to determine transition rules and durations
    /// Interfaces with UnifiedAnimationController for actual animation playback
    /// </summary>
    public class AnimationTransitionManager : MonoBehaviour
    {
        #region Inspector Fields

        [Header("Core Components")]
        [SerializeField] private UnifiedAnimationController animationController;
        [SerializeField] private AnimationConfigurationManager configManager;

        [Header("Transition Settings")]
        [SerializeField] private bool enableTransitionValidation = true;
        [SerializeField] private bool enableTransitionQueue = true;
        [SerializeField] private bool enableInterruptibleTransitions = true;
        [SerializeField] private float transitionTimeoutDuration = 5f;

        [Header("Debug Settings")]
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private bool showTransitionGizmos = false;
        [SerializeField] private bool trackTransitionHistory = true;

        #endregion

        #region Private Fields

        private readonly Queue<TransitionRequest> transitionQueue = new Queue<TransitionRequest>();
        private readonly List<TransitionHistory> transitionHistory = new List<TransitionHistory>();
        private readonly Dictionary<string, TransitionRule> customTransitionRules = new Dictionary<string, TransitionRule>();

        private TransitionRequest currentTransition;
        private Coroutine activeTransitionCoroutine;
        private bool isInitialized = false;
        private float lastTransitionTime;

        // Performance tracking
        private int totalTransitions = 0;
        private int failedTransitions = 0;
        private float averageTransitionDuration = 0f;

        #endregion

        #region Properties

        /// <summary>
        /// Whether the transition manager is properly initialized
        /// </summary>
        public bool IsInitialized => isInitialized;

        /// <summary>
        /// Whether a transition is currently in progress
        /// </summary>
        public bool IsTransitioning => currentTransition != null && !currentTransition.IsCompleted;

        /// <summary>
        /// Number of queued transitions
        /// </summary>
        public int QueuedTransitionCount => transitionQueue.Count;

        /// <summary>
        /// Current transition request (if any)
        /// </summary>
        public TransitionRequest CurrentTransition => currentTransition;

        /// <summary>
        /// Transition performance statistics
        /// </summary>
        public TransitionStatistics Statistics => new TransitionStatistics
        {
            totalTransitions = totalTransitions,
            failedTransitions = failedTransitions,
            successRate = totalTransitions > 0 ? (float)(totalTransitions - failedTransitions) / totalTransitions : 0f,
            averageDuration = averageTransitionDuration
        };

        #endregion

        #region Events

        /// <summary>
        /// Event fired when a transition starts
        /// </summary>
        public event Action<TransitionRequest> OnTransitionStarted;

        /// <summary>
        /// Event fired when a transition completes successfully
        /// </summary>
        public event Action<TransitionRequest> OnTransitionCompleted;

        /// <summary>
        /// Event fired when a transition fails
        /// </summary>
        public event Action<TransitionRequest, string> OnTransitionFailed;

        /// <summary>
        /// Event fired when a transition is interrupted
        /// </summary>
        public event Action<TransitionRequest, TransitionRequest> OnTransitionInterrupted;

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeTransitionManager();
        }

        private void Start()
        {
            if (!isInitialized)
            {
                InitializeTransitionManager();
            }
        }

        private void Update()
        {
            ProcessTransitionQueue();
            UpdateCurrentTransition();
            CheckTransitionTimeouts();
        }

        private void OnDestroy()
        {
            CleanupTransitionManager();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the transition manager
        /// </summary>
        private void InitializeTransitionManager()
        {
            try
            {
                // Find required components
                if (animationController == null)
                    animationController = FindObjectOfType<UnifiedAnimationController>();

                if (configManager == null)
                    configManager = AnimationConfigurationManager.Instance;

                if (animationController == null)
                {
                    Debug.LogError("[AnimationTransitionManager] UnifiedAnimationController not found!");
                    return;
                }

                if (configManager == null)
                {
                    Debug.LogError("[AnimationTransitionManager] AnimationConfigurationManager not found!");
                    return;
                }

                // Subscribe to events
                SubscribeToEvents();

                // Initialize custom transition rules
                InitializeTransitionRules();

                isInitialized = true;
                LogDebug("AnimationTransitionManager initialized successfully");
            }
            catch (Exception e)
            {
                Debug.LogError($"[AnimationTransitionManager] Initialization failed: {e.Message}");
                isInitialized = false;
            }
        }

        /// <summary>
        /// Subscribe to relevant events
        /// </summary>
        private void SubscribeToEvents()
        {
            // Subscribe to animation controller events
            if (animationController != null)
            {
                animationController.OnAnimationStateChanged += OnAnimationStateChanged;
                animationController.OnAnimationTransitionStarted += OnAnimationControllerTransitionStarted;
                animationController.OnAnimationTransitionCompleted += OnAnimationControllerTransitionCompleted;
            }

            // Subscribe to configuration manager events
            if (configManager != null)
            {
                configManager.OnConfigurationChanged += OnConfigurationChanged;
            }

            // Subscribe to game events
            EventManager.Subscribe<OnAnimationChangeRequestEvent>(OnAnimationChangeRequest);
        }

        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            // Unsubscribe from animation controller events
            if (animationController != null)
            {
                animationController.OnAnimationStateChanged -= OnAnimationStateChanged;
                animationController.OnAnimationTransitionStarted -= OnAnimationControllerTransitionStarted;
                animationController.OnAnimationTransitionCompleted -= OnAnimationControllerTransitionCompleted;
            }

            // Unsubscribe from configuration manager events
            if (configManager != null)
            {
                configManager.OnConfigurationChanged -= OnConfigurationChanged;
            }

            // Unsubscribe from game events
            EventManager.Unsubscribe<OnAnimationChangeRequestEvent>(OnAnimationChangeRequest);
        }

        /// <summary>
        /// Initialize custom transition rules
        /// </summary>
        private void InitializeTransitionRules()
        {
            // Add default transition rules
            AddTransitionRule("StandingToWalking", new TransitionRule
            {
                fromState = "Standing",
                toState = "Walking",
                requiredConditions = new List<string> { "InputMagnitude > 0.1" },
                priority = TransitionPriority.Medium,
                canInterrupt = true,
                customDuration = -1f // Use configuration default
            });

            AddTransitionRule("WalkingToStopping", new TransitionRule
            {
                fromState = "Walking",
                toState = "WalkingStop",
                requiredConditions = new List<string> { "InputMagnitude < 0.1" },
                priority = TransitionPriority.Medium,
                canInterrupt = false,
                customDuration = 0.2f
            });

            AddTransitionRule("IdleToAiming", new TransitionRule
            {
                fromState = "Idle",
                toState = "Aiming",
                requiredConditions = new List<string> { "HasTarget", "IsAiming" },
                priority = TransitionPriority.High,
                canInterrupt = true,
                customDuration = 0.15f
            });

            AddTransitionRule("AimingToShooting", new TransitionRule
            {
                fromState = "Aiming",
                toState = "Shooting",
                requiredConditions = new List<string> { "IsShooting" },
                priority = TransitionPriority.Critical,
                canInterrupt = false,
                customDuration = 0.1f
            });

            LogDebug($"Initialized {customTransitionRules.Count} transition rules");
        }

        /// <summary>
        /// Cleanup the transition manager
        /// </summary>
        private void CleanupTransitionManager()
        {
            // Stop any active transitions
            if (activeTransitionCoroutine != null)
            {
                StopCoroutine(activeTransitionCoroutine);
                activeTransitionCoroutine = null;
            }

            // Clear queues and history
            transitionQueue.Clear();
            transitionHistory.Clear();
            customTransitionRules.Clear();

            // Unsubscribe from events
            UnsubscribeFromEvents();

            LogDebug("AnimationTransitionManager cleaned up");
        }

        #endregion

        #region Transition Management

        /// <summary>
        /// Request a transition to a new animation state
        /// </summary>
        public bool RequestTransition(AnimationStateContext targetContext, TransitionPriority priority = TransitionPriority.Medium,
            float customDuration = -1f, bool forceTransition = false, string reason = "")
        {
            if (!isInitialized)
            {
                LogDebug("Cannot request transition - manager not initialized");
                return false;
            }

            // Create transition request
            var request = new TransitionRequest
            {
                targetContext = targetContext,
                priority = priority,
                customDuration = customDuration,
                forceTransition = forceTransition,
                reason = reason,
                requestTime = Time.time,
                sourceContext = animationController?.CurrentContext ?? AnimationStateContext.Default
            };

            // Validate transition if enabled
            if (enableTransitionValidation && !ValidateTransition(request, out string validationError))
            {
                LogDebug($"Transition validation failed: {validationError}");
                OnTransitionFailed?.Invoke(request, validationError);
                failedTransitions++;
                return false;
            }

            // Handle immediate vs queued transitions
            if (forceTransition || !IsTransitioning || CanInterruptCurrentTransition(request))
            {
                return ExecuteTransition(request);
            }
            else if (enableTransitionQueue)
            {
                QueueTransition(request);
                return true;
            }
            else
            {
                LogDebug($"Transition rejected - already transitioning and cannot interrupt");
                OnTransitionFailed?.Invoke(request, "Cannot interrupt current transition");
                failedTransitions++;
                return false;
            }
        }

        /// <summary>
        /// Execute a transition immediately
        /// </summary>
        private bool ExecuteTransition(TransitionRequest request)
        {
            try
            {
                // Interrupt current transition if necessary
                if (IsTransitioning && enableInterruptibleTransitions)
                {
                    InterruptCurrentTransition(request);
                }

                // Set as current transition
                currentTransition = request;
                currentTransition.startTime = Time.time;

                // Calculate transition duration
                float duration = CalculateTransitionDuration(request);
                currentTransition.duration = duration;

                // Start transition coroutine
                activeTransitionCoroutine = StartCoroutine(ExecuteTransitionCoroutine(request));

                // Fire events
                OnTransitionStarted?.Invoke(request);

                // Broadcast event
                var transitionEvent = new OnAnimationTransitionStartedEvent(
                    request.sourceContext.movementState.ToString(),
                    request.targetContext.movementState.ToString(),
                    duration, false, true);
                EventManager.Broadcast(transitionEvent);

                LogDebug($"Started transition: {request.reason} (Duration: {duration:F2}s, Priority: {request.priority})");

                totalTransitions++;
                lastTransitionTime = Time.time;

                return true;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AnimationTransitionManager] Failed to execute transition: {e.Message}");
                OnTransitionFailed?.Invoke(request, e.Message);
                failedTransitions++;
                return false;
            }
        }

        /// <summary>
        /// Coroutine that handles the actual transition execution
        /// </summary>
        private IEnumerator ExecuteTransitionCoroutine(TransitionRequest request)
        {
            float startTime = Time.time;

            // Apply the transition to the animation controller
            if (animationController != null)
            {
                animationController.SetAnimationContext(request.targetContext);
            }

            // Wait for transition duration
            yield return new WaitForSeconds(request.duration);

            // Mark transition as completed
            request.IsCompleted = true;
            request.completionTime = Time.time;

            // Update statistics
            float actualDuration = Time.time - startTime;
            UpdateAverageTransitionDuration(actualDuration);

            // Add to history
            if (trackTransitionHistory)
            {
                AddToTransitionHistory(request, true, "");
            }

            // Fire completion event
            OnTransitionCompleted?.Invoke(request);

            // Broadcast completion event
            var completionEvent = new OnAnimationTransitionCompletedEvent(
                request.targetContext.movementState.ToString(), actualDuration);
            EventManager.Broadcast(completionEvent);

            LogDebug($"Completed transition: {request.reason} (Actual duration: {actualDuration:F2}s)");

            // Clear current transition
            if (currentTransition == request)
            {
                currentTransition = null;
            }
        }

        /// <summary>
        /// Queue a transition for later execution
        /// </summary>
        private void QueueTransition(TransitionRequest request)
        {
            transitionQueue.Enqueue(request);
            LogDebug($"Queued transition: {request.reason} (Queue size: {transitionQueue.Count})");
        }

        /// <summary>
        /// Process the transition queue
        /// </summary>
        private void ProcessTransitionQueue()
        {
            if (!IsTransitioning && transitionQueue.Count > 0)
            {
                var nextRequest = transitionQueue.Dequeue();

                // Check if the queued transition is still valid
                if (Time.time - nextRequest.requestTime < transitionTimeoutDuration)
                {
                    ExecuteTransition(nextRequest);
                }
                else
                {
                    LogDebug($"Discarded expired transition: {nextRequest.reason}");
                    OnTransitionFailed?.Invoke(nextRequest, "Transition expired in queue");
                    failedTransitions++;
                }
            }
        }

        /// <summary>
        /// Update current transition state
        /// </summary>
        private void UpdateCurrentTransition()
        {
            if (currentTransition != null && !currentTransition.IsCompleted)
            {
                currentTransition.progress = Mathf.Clamp01((Time.time - currentTransition.startTime) / currentTransition.duration);
            }
        }

        /// <summary>
        /// Check for transition timeouts
        /// </summary>
        private void CheckTransitionTimeouts()
        {
            if (currentTransition != null && !currentTransition.IsCompleted)
            {
                float elapsedTime = Time.time - currentTransition.startTime;
                if (elapsedTime > currentTransition.duration + 1f) // 1 second grace period
                {
                    LogDebug($"Transition timeout detected: {currentTransition.reason}");

                    // Force completion
                    if (activeTransitionCoroutine != null)
                    {
                        StopCoroutine(activeTransitionCoroutine);
                        activeTransitionCoroutine = null;
                    }

                    OnTransitionFailed?.Invoke(currentTransition, "Transition timeout");
                    currentTransition = null;
                    failedTransitions++;
                }
            }
        }

        #endregion

        #region Validation and Rules

        /// <summary>
        /// Validate a transition request
        /// </summary>
        private bool ValidateTransition(TransitionRequest request, out string error)
        {
            error = string.Empty;

            // Check if contexts are valid
            if (!AnimationStateContextUtility.IsValidContext(request.sourceContext, out string sourceError))
            {
                error = $"Invalid source context: {sourceError}";
                return false;
            }

            if (!AnimationStateContextUtility.IsValidContext(request.targetContext, out string targetError))
            {
                error = $"Invalid target context: {targetError}";
                return false;
            }

            // Check if transition is valid
            if (!AnimationStateContextUtility.IsValidTransition(request.sourceContext, request.targetContext, out string transitionError))
            {
                error = $"Invalid transition: {transitionError}";
                return false;
            }

            // Check custom transition rules
            string ruleKey = $"{request.sourceContext.movementState}To{request.targetContext.movementState}";
            if (customTransitionRules.TryGetValue(ruleKey, out var rule))
            {
                if (!ValidateTransitionRule(request, rule, out string ruleError))
                {
                    error = $"Rule validation failed: {ruleError}";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Validate a specific transition rule
        /// </summary>
        private bool ValidateTransitionRule(TransitionRequest request, TransitionRule rule, out string error)
        {
            error = string.Empty;

            // Check required conditions
            foreach (var condition in rule.requiredConditions)
            {
                if (!EvaluateCondition(condition, request))
                {
                    error = $"Condition not met: {condition}";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Evaluate a transition condition
        /// </summary>
        private bool EvaluateCondition(string condition, TransitionRequest request)
        {
            var context = request.targetContext;

            return condition switch
            {
                "InputMagnitude > 0.1" => context.inputMagnitude > 0.1f,
                "InputMagnitude < 0.1" => context.inputMagnitude < 0.1f,
                "HasTarget" => context.detectTarget,
                "IsAiming" => context.isAiming,
                "IsShooting" => context.isShooting,
                "IsGrounded" => context.isGrounded,
                _ => true // Unknown conditions pass by default
            };
        }

        /// <summary>
        /// Check if current transition can be interrupted
        /// </summary>
        private bool CanInterruptCurrentTransition(TransitionRequest newRequest)
        {
            if (currentTransition == null) return true;

            // Higher priority transitions can interrupt lower priority ones
            if (newRequest.priority > currentTransition.priority) return true;

            // Check if current transition allows interruption
            string ruleKey = $"{currentTransition.sourceContext.movementState}To{currentTransition.targetContext.movementState}";
            if (customTransitionRules.TryGetValue(ruleKey, out var rule))
            {
                return rule.canInterrupt;
            }

            // Default: allow interruption if enabled
            return enableInterruptibleTransitions;
        }

        /// <summary>
        /// Interrupt the current transition
        /// </summary>
        private void InterruptCurrentTransition(TransitionRequest newRequest)
        {
            if (currentTransition == null) return;

            LogDebug($"Interrupting transition: {currentTransition.reason} with {newRequest.reason}");

            // Stop current transition coroutine
            if (activeTransitionCoroutine != null)
            {
                StopCoroutine(activeTransitionCoroutine);
                activeTransitionCoroutine = null;
            }

            // Mark as interrupted
            currentTransition.IsCompleted = true;
            currentTransition.completionTime = Time.time;

            // Add to history
            if (trackTransitionHistory)
            {
                AddToTransitionHistory(currentTransition, false, "Interrupted");
            }

            // Fire interruption event
            OnTransitionInterrupted?.Invoke(currentTransition, newRequest);

            // Clear current transition
            currentTransition = null;
        }

        /// <summary>
        /// Add a custom transition rule
        /// </summary>
        public void AddTransitionRule(string key, TransitionRule rule)
        {
            customTransitionRules[key] = rule;
            LogDebug($"Added transition rule: {key}");
        }

        /// <summary>
        /// Remove a custom transition rule
        /// </summary>
        public bool RemoveTransitionRule(string key)
        {
            bool removed = customTransitionRules.Remove(key);
            if (removed)
            {
                LogDebug($"Removed transition rule: {key}");
            }
            return removed;
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Calculate transition duration based on request and configuration
        /// </summary>
        private float CalculateTransitionDuration(TransitionRequest request)
        {
            // Use custom duration if specified
            if (request.customDuration > 0f)
            {
                return request.customDuration;
            }

            // Check for rule-specific duration
            string ruleKey = $"{request.sourceContext.movementState}To{request.targetContext.movementState}";
            if (customTransitionRules.TryGetValue(ruleKey, out var rule) && rule.customDuration > 0f)
            {
                return rule.customDuration;
            }

            // Use configuration-based duration
            if (configManager != null)
            {
                return configManager.GetTransitionDuration(request.priority);
            }

            // Fallback to utility method
            return AnimationStateContextUtility.GetRecommendedTransitionDuration(request.sourceContext, request.targetContext);
        }

        /// <summary>
        /// Update average transition duration for statistics
        /// </summary>
        private void UpdateAverageTransitionDuration(float duration)
        {
            if (totalTransitions == 0)
            {
                averageTransitionDuration = duration;
            }
            else
            {
                averageTransitionDuration = (averageTransitionDuration * (totalTransitions - 1) + duration) / totalTransitions;
            }
        }

        /// <summary>
        /// Add transition to history
        /// </summary>
        private void AddToTransitionHistory(TransitionRequest request, bool success, string errorMessage)
        {
            var historyEntry = new TransitionHistory
            {
                request = request,
                success = success,
                errorMessage = errorMessage,
                timestamp = Time.time
            };

            transitionHistory.Add(historyEntry);

            // Limit history size
            if (transitionHistory.Count > 100)
            {
                transitionHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// Log debug message if logging is enabled
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[AnimationTransitionManager] {message}");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle animation state changes from the controller
        /// </summary>
        private void OnAnimationStateChanged(AnimationStateContext previous, AnimationStateContext current)
        {
            LogDebug($"Animation state changed: {previous.movementState} → {current.movementState}");
        }

        /// <summary>
        /// Handle transition started events from the controller
        /// </summary>
        private void OnAnimationControllerTransitionStarted(string fromState, string toState, float duration)
        {
            LogDebug($"Animation controller transition started: {fromState} → {toState} ({duration:F2}s)");
        }

        /// <summary>
        /// Handle transition completed events from the controller
        /// </summary>
        private void OnAnimationControllerTransitionCompleted(string stateName)
        {
            LogDebug($"Animation controller transition completed: {stateName}");
        }

        /// <summary>
        /// Handle configuration changes
        /// </summary>
        private void OnConfigurationChanged(AnimationSystemConfiguration newConfig)
        {
            LogDebug($"Configuration changed to: {newConfig.name}");

            // Update transition settings based on new configuration
            if (newConfig.debugConfig != null)
            {
                enableDebugLogging = newConfig.debugConfig.logStateTransitions;
                showTransitionGizmos = newConfig.debugConfig.showTransitionTimelines;
            }
        }

        /// <summary>
        /// Handle animation change requests from events
        /// </summary>
        private void OnAnimationChangeRequest(OnAnimationChangeRequestEvent eventData)
        {
            // Convert event data to animation context
            var targetContext = new AnimationStateContext
            {
                movementState = eventData.MovementState,
                weaponState = eventData.WeaponState,
                weaponModuleState = eventData.WeaponModule,
                isAiming = eventData.IsAiming,
                isShooting = eventData.IsShooting,
                inputMagnitude = eventData.InputMagnitude,
                inputDirection = eventData.InputDirection,
                isMoving = eventData.InputMagnitude > 0.1f,
                isGrounded = true,
                upperBodyWeight = 1f,
                lowerBodyWeight = 1f,
                stateChangeTime = Time.time
            };

            // Determine priority based on change type
            var priority = TransitionPriority.Medium;
            if (eventData.IsAiming || eventData.IsShooting)
                priority = TransitionPriority.High;

            // Request transition
            RequestTransition(targetContext, priority, eventData.CustomDuration,
                eventData.ForceTransition, "Event Request");
        }

        #endregion

        #region Public API

        /// <summary>
        /// Request a movement state transition
        /// </summary>
        public bool RequestMovementTransition(MovementSubState targetState, float inputMagnitude = 0f,
            Vector3 inputDirection = default, string reason = "Movement Request")
        {
            var currentContext = animationController?.CurrentContext ?? AnimationStateContext.Default;
            var targetContext = currentContext;
            targetContext.movementState = targetState;
            targetContext.inputMagnitude = inputMagnitude;
            targetContext.inputDirection = inputDirection;
            targetContext.isMoving = inputMagnitude > 0.1f;

            return RequestTransition(targetContext, TransitionPriority.Medium, -1f, false, reason);
        }

        /// <summary>
        /// Request a weapon state transition
        /// </summary>
        public bool RequestWeaponTransition(WeaponSubState targetState, WeaponSubModuleState module = WeaponSubModuleState.EmptyHand,
            bool isAiming = false, bool isShooting = false, string reason = "Weapon Request")
        {
            var currentContext = animationController?.CurrentContext ?? AnimationStateContext.Default;
            var targetContext = currentContext;
            targetContext.weaponState = targetState;
            targetContext.weaponModuleState = module;
            targetContext.isAiming = isAiming;
            targetContext.isShooting = isShooting;

            return RequestTransition(targetContext, TransitionPriority.High, -1f, false, reason);
        }

        /// <summary>
        /// Request a combat mode transition
        /// </summary>
        public bool RequestCombatTransition(bool enterCombat, Vector3 targetPosition = default,
            string reason = "Combat Request")
        {
            var currentContext = animationController?.CurrentContext ?? AnimationStateContext.Default;
            var targetContext = currentContext;
            targetContext.mainState = enterCombat ? MainState.Combat : MainState.Normal;
            targetContext.aimTarget = targetPosition;
            targetContext.detectTarget = enterCombat;

            if (enterCombat)
            {
                targetContext.weaponState = WeaponSubState.Aiming;
                targetContext.isAiming = true;
                if (targetContext.weaponModuleState == WeaponSubModuleState.EmptyHand)
                {
                    targetContext.weaponModuleState = WeaponSubModuleState.Pistol;
                }
            }

            return RequestTransition(targetContext, TransitionPriority.Critical, -1f, false, reason);
        }

        /// <summary>
        /// Force stop all transitions
        /// </summary>
        public void StopAllTransitions()
        {
            // Stop current transition
            if (activeTransitionCoroutine != null)
            {
                StopCoroutine(activeTransitionCoroutine);
                activeTransitionCoroutine = null;
            }

            // Clear current transition
            if (currentTransition != null)
            {
                OnTransitionFailed?.Invoke(currentTransition, "Force stopped");
                currentTransition = null;
            }

            // Clear queue
            transitionQueue.Clear();

            LogDebug("All transitions stopped");
        }

        /// <summary>
        /// Get transition history
        /// </summary>
        public List<TransitionHistory> GetTransitionHistory()
        {
            return new List<TransitionHistory>(transitionHistory);
        }

        /// <summary>
        /// Clear transition history
        /// </summary>
        public void ClearTransitionHistory()
        {
            transitionHistory.Clear();
            LogDebug("Transition history cleared");
        }

        /// <summary>
        /// Get all custom transition rules
        /// </summary>
        public Dictionary<string, TransitionRule> GetTransitionRules()
        {
            return new Dictionary<string, TransitionRule>(customTransitionRules);
        }

        #endregion
    }
}