using System;
using System.Collections.Generic;
using UnityEngine;
using PlayerFAP.ScriptableObjects;
using Animation.Core;

namespace PlayerFAP.Managers
{
    /// <summary>
    /// Singleton manager for animation configuration system
    /// Handles loading, caching, and accessing animation configurations
    /// </summary>
    public class AnimationConfigurationManager : MonoBehaviour
    {
        #region Singleton
        
        private static AnimationConfigurationManager _instance;
        public static AnimationConfigurationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<AnimationConfigurationManager>();
                    if (_instance == null)
                    {
                        var go = new GameObject("AnimationConfigurationManager");
                        _instance = go.AddComponent<AnimationConfigurationManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Inspector Fields
        
        [Header("Configuration Assets")]
        [SerializeField] private AnimationSystemConfiguration systemConfiguration;
        [SerializeField] private List<AnimationSystemConfiguration> additionalConfigurations;
        
        [Header("Runtime Settings")]
        [SerializeField] private bool autoLoadConfigurations = true;
        [SerializeField] private bool enableConfigurationCaching = true;
        [SerializeField] private bool validateConfigurationsOnLoad = true;
        
        #endregion
        
        #region Private Fields
        
        private readonly Dictionary<string, AnimationSystemConfiguration> configurationCache = 
            new Dictionary<string, AnimationSystemConfiguration>();
        
        private bool isInitialized = false;
        private AnimationSystemConfiguration _currentConfiguration;
        
        #endregion
        
        #region Properties
        
        /// <summary>
        /// Current active animation system configuration
        /// </summary>
        public AnimationSystemConfiguration CurrentConfiguration
        {
            get
            {
                if (_currentConfiguration == null)
                {
                    LoadDefaultConfiguration();
                }
                return _currentConfiguration;
            }
        }
        
        /// <summary>
        /// Whether the manager has been properly initialized
        /// </summary>
        public bool IsInitialized => isInitialized;
        
        /// <summary>
        /// Number of cached configurations
        /// </summary>
        public int CachedConfigurationCount => configurationCache.Count;
        
        #endregion
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                
                if (autoLoadConfigurations)
                {
                    Initialize();
                }
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (!isInitialized && autoLoadConfigurations)
            {
                Initialize();
            }
        }
        
        #endregion
        
        #region Initialization
        
        /// <summary>
        /// Initialize the configuration manager
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
            {
                Debug.LogWarning("[AnimationConfigurationManager] Already initialized");
                return;
            }
            
            try
            {
                LoadConfigurations();
                isInitialized = true;
                Debug.Log("[AnimationConfigurationManager] Initialized successfully");
            }
            catch (Exception e)
            {
                Debug.LogError($"[AnimationConfigurationManager] Initialization failed: {e.Message}");
                isInitialized = false;
            }
        }
        
        /// <summary>
        /// Load all animation configurations
        /// </summary>
        private void LoadConfigurations()
        {
            // Clear existing cache
            configurationCache.Clear();
            
            // Load default configuration
            LoadDefaultConfiguration();
            
            // Load additional configurations
            LoadAdditionalConfigurations();
            
            // Load configurations from Resources folder
            LoadResourceConfigurations();
            
            Debug.Log($"[AnimationConfigurationManager] Loaded {configurationCache.Count} configurations");
        }
        
        /// <summary>
        /// Load the default configuration
        /// </summary>
        private void LoadDefaultConfiguration()
        {
            if (systemConfiguration != null)
            {
                _currentConfiguration = systemConfiguration;
                CacheConfiguration("default", systemConfiguration);
                
                if (validateConfigurationsOnLoad)
                {
                    ValidateConfiguration(systemConfiguration);
                }
            }
            else
            {
                // Try to load from Resources
                var resourceConfig = Resources.Load<AnimationSystemConfiguration>("AnimationSystemConfiguration");
                if (resourceConfig != null)
                {
                    _currentConfiguration = resourceConfig;
                    systemConfiguration = resourceConfig;
                    CacheConfiguration("default", resourceConfig);
                }
                else
                {
                    // Create a default configuration
                    CreateDefaultConfiguration();
                }
            }
        }
        
        /// <summary>
        /// Load additional configurations from inspector
        /// </summary>
        private void LoadAdditionalConfigurations()
        {
            if (additionalConfigurations == null) return;
            
            foreach (var config in additionalConfigurations)
            {
                if (config != null)
                {
                    CacheConfiguration(config.name, config);
                    
                    if (validateConfigurationsOnLoad)
                    {
                        ValidateConfiguration(config);
                    }
                }
            }
        }
        
        /// <summary>
        /// Load configurations from Resources folder
        /// </summary>
        private void LoadResourceConfigurations()
        {
            var configs = Resources.LoadAll<AnimationSystemConfiguration>("");
            foreach (var config in configs)
            {
                if (config != null && !configurationCache.ContainsKey(config.name))
                {
                    CacheConfiguration(config.name, config);
                    
                    if (validateConfigurationsOnLoad)
                    {
                        ValidateConfiguration(config);
                    }
                }
            }
        }
        
        /// <summary>
        /// Create a default configuration if none exists
        /// </summary>
        private void CreateDefaultConfiguration()
        {
            _currentConfiguration = ScriptableObject.CreateInstance<AnimationSystemConfiguration>();
            _currentConfiguration.name = "DefaultAnimationConfiguration";
            
            // Set default values
            _currentConfiguration.useAnimancerByDefault = true;
            _currentConfiguration.enableDebugLogging = false;
            _currentConfiguration.defaultTransitionDuration = 0.25f;
            _currentConfiguration.updateRate = 30f;
            
            CacheConfiguration("default", _currentConfiguration);
            
            Debug.LogWarning("[AnimationConfigurationManager] Created default configuration. " +
                           "Consider creating a proper AnimationSystemConfiguration asset.");
        }
        
        #endregion
        
        #region Configuration Management
        
        /// <summary>
        /// Cache a configuration with a given key
        /// </summary>
        private void CacheConfiguration(string key, AnimationSystemConfiguration config)
        {
            if (enableConfigurationCaching)
            {
                configurationCache[key] = config;
            }
        }
        
        /// <summary>
        /// Get a configuration by name
        /// </summary>
        public AnimationSystemConfiguration GetConfiguration(string name)
        {
            if (configurationCache.TryGetValue(name, out var config))
            {
                return config;
            }
            
            Debug.LogWarning($"[AnimationConfigurationManager] Configuration '{name}' not found");
            return CurrentConfiguration; // Return default
        }
        
        /// <summary>
        /// Switch to a different configuration
        /// </summary>
        public bool SwitchConfiguration(string name)
        {
            var config = GetConfiguration(name);
            if (config != null && config != _currentConfiguration)
            {
                _currentConfiguration = config;
                OnConfigurationChanged?.Invoke(_currentConfiguration);
                Debug.Log($"[AnimationConfigurationManager] Switched to configuration: {name}");
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// Validate a configuration
        /// </summary>
        private void ValidateConfiguration(AnimationSystemConfiguration config)
        {
            if (config == null)
            {
                Debug.LogError("[AnimationConfigurationManager] Configuration is null");
                return;
            }
            
            // Validate transition configuration
            if (config.transitionConfig == null)
            {
                Debug.LogWarning($"[AnimationConfigurationManager] Configuration '{config.name}' has null transition config");
            }
            
            // Validate performance configuration
            if (config.performanceConfig == null)
            {
                Debug.LogWarning($"[AnimationConfigurationManager] Configuration '{config.name}' has null performance config");
            }
            
            // Validate LOD configuration
            if (config.lodConfig == null)
            {
                Debug.LogWarning($"[AnimationConfigurationManager] Configuration '{config.name}' has null LOD config");
            }
            
            // Validate state mappings
            if (config.movementStateMappings == null || config.movementStateMappings.Count == 0)
            {
                Debug.LogWarning($"[AnimationConfigurationManager] Configuration '{config.name}' has no movement state mappings");
            }
            
            if (config.weaponStateMappings == null || config.weaponStateMappings.Count == 0)
            {
                Debug.LogWarning($"[AnimationConfigurationManager] Configuration '{config.name}' has no weapon state mappings");
            }
        }
        
        /// <summary>
        /// Reload all configurations
        /// </summary>
        public void ReloadConfigurations()
        {
            LoadConfigurations();
            OnConfigurationChanged?.Invoke(_currentConfiguration);
        }
        
        /// <summary>
        /// Get all available configuration names
        /// </summary>
        public List<string> GetAvailableConfigurationNames()
        {
            return new List<string>(configurationCache.Keys);
        }
        
        #endregion
        
        #region Events
        
        /// <summary>
        /// Event fired when the current configuration changes
        /// </summary>
        public event Action<AnimationSystemConfiguration> OnConfigurationChanged;
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Get transition duration for a specific priority
        /// </summary>
        public float GetTransitionDuration(TransitionPriority priority)
        {
            return CurrentConfiguration.GetTransitionDuration(priority);
        }
        
        /// <summary>
        /// Get animation clip for movement state
        /// </summary>
        public AnimationClip GetMovementAnimation(MovementSubState state)
        {
            return CurrentConfiguration.GetMovementAnimation(state);
        }
        
        /// <summary>
        /// Get animation clip for weapon state
        /// </summary>
        public AnimationClip GetWeaponAnimation(WeaponSubState state, WeaponSubModuleState module)
        {
            return CurrentConfiguration.GetWeaponAnimation(state, module);
        }
        
        /// <summary>
        /// Check if LOD should be applied
        /// </summary>
        public bool ShouldApplyLOD(float distance)
        {
            return CurrentConfiguration.ShouldApplyLOD(distance);
        }
        
        /// <summary>
        /// Get LOD level for distance
        /// </summary>
        public int GetLODLevel(float distance)
        {
            return CurrentConfiguration.GetLODLevel(distance);
        }
        
        #endregion
    }
}
