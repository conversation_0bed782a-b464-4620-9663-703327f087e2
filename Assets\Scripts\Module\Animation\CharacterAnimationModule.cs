using Module.Mono.Modules;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public class CharacterAnimationModule : AnimationModuleWithStates<CharacterState, MovementSubState>
    {
        /// <summary>
        /// Applies rotation through animation instead of transform rotation
        /// </summary>
        /// <param name="rotateAmount">Amount to rotate in degrees</param>
        public void InPlaceRotation(float rotateAmount)
        {
            DebugLogManager.Instance.Log($"[CharacterAnimationModule] IN-PLACE ROTATION - Value: {rotateAmount:F2}", DebugLogSettings.LogType.PlayerMovement);

            try
            {
                // Check if States dictionary is valid
                if (States == null)
                {
                    DebugLogManager.Instance.Log($"[CharacterAnimationModule] ERROR - States dictionary is NULL!", DebugLogSettings.LogType.PlayerMovement);
                    return;
                }

                // Check if Standing state exists
                if (!States.ContainsKey(MovementSubState.Standing))
                {
                    DebugLogManager.Instance.Log($"[CharacterAnimationModule] ERROR - Standing state not found in States dictionary!", DebugLogSettings.LogType.PlayerMovement);
                    return;
                }

                // Check if Standing state is IdleState
                var idleState = States[MovementSubState.Standing] as IdleState;
                if (idleState == null)
                {
                    DebugLogManager.Instance.Log($"[CharacterAnimationModule] ERROR - Standing state is not IdleState! Type: {States[MovementSubState.Standing].GetType().Name}", DebugLogSettings.LogType.PlayerMovement);
                    return;
                }

                // Forward the call to IdleState
                idleState.ApplyInPlaceRotation(rotateAmount);
                DebugLogManager.Instance.Log($"[CharacterAnimationModule] Successfully forwarded rotation to IdleState", DebugLogSettings.LogType.PlayerMovement);
            }
            catch (System.Exception e)
            {
                DebugLogManager.Instance.Log($"[CharacterAnimationModule] EXCEPTION in InPlaceRotation: {e.Message}\n{e.StackTrace}", DebugLogSettings.LogType.PlayerMovement);
            }
        }
    }
}