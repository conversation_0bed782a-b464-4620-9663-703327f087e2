%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &35541296577206112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6809340320723751594}
  m_Layer: 0
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6809340320723751594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 35541296577206112}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071063, y: 0.000010983767, z: -0.000016404654, w: 0.70710725}
  m_LocalPosition: {x: -0, y: 0, z: 1.1449527}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2682979858017989496}
  - {fileID: 1246016646434924394}
  - {fileID: 3396170966632270810}
  m_Father: {fileID: 37560226843851422}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &183981348275227477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7704864047249165022}
  m_Layer: 0
  m_Name: RaycasterFront
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7704864047249165022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183981348275227477}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.459, z: 0.189}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &212442194028064745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 968039333386813128}
  m_Layer: 21
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &968039333386813128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 212442194028064745}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05011441, y: 0.6748035, z: -0.73562753, w: 0.031319}
  m_LocalPosition: {x: 0, y: 0.06, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8166452957145200494}
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: -108.337, y: -265.796, z: 272.1}
--- !u!1 &309953741149805596
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7507255572970579955}
  - component: {fileID: 5562475231938505990}
  - component: {fileID: 2210462193945988030}
  - component: {fileID: 2443512780624999051}
  - component: {fileID: 720600813861139802}
  - component: {fileID: 2757235582597895822}
  - component: {fileID: 7849422468994326215}
  - component: {fileID: 9169839010926252671}
  - component: {fileID: 554600306175030877}
  m_Layer: 6
  m_Name: InteractionModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7507255572970579955
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4730494400458778153}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5562475231938505990
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c08dd31e7f694fdcaf3d250569c31167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_interactor: {fileID: 2443512780624999051}
  <InputName>k__BackingField: 14000000
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 
  _interactionSubState: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  m_isInteracting: 0
--- !u!135 &2210462193945988030
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 2097152
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1.3668647
  m_Center: {x: 0, y: 0.84438926, z: 0}
--- !u!114 &2443512780624999051
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c250986fe58e08148abc5e673e3e4fe4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  anyConnected: 0
  effectorLinks:
  - targetActive: 0
    targetPosition: {x: 0, y: 0, z: 0}
    effectorWorldSpace: {x: 0, y: 0, z: 0}
    enabled: 1
    effectorName: LH
    effectorType: 5
    posOffset: {x: -0.141, y: 0.624, z: -0.046}
    angleOffset: -10
    angleXZ: 130
    angleOffsetYZ: 30
    angleYZ: 120
    maxRadius: 0.6708473
    minRadius: 0.2012542
  - targetActive: 0
    targetPosition: {x: 0, y: 0, z: 0}
    effectorWorldSpace: {x: 0, y: 0, z: 0}
    enabled: 1
    effectorName: RH
    effectorType: 6
    posOffset: {x: 0.141, y: 0.624, z: -0.046}
    angleOffset: 60
    angleXZ: 130
    angleOffsetYZ: 30
    angleYZ: 120
    maxRadius: 0.67085
    minRadius: 0.20125501
  - targetActive: 0
    targetPosition: {x: 0, y: 0, z: 0}
    effectorWorldSpace: {x: 0, y: 0, z: 0}
    enabled: 1
    effectorName: Body
    effectorType: 0
    posOffset: {x: 0, y: 0, z: 0}
    angleOffset: 0
    angleXZ: 45
    angleOffsetYZ: 0
    angleYZ: 45
    maxRadius: 0.1
    minRadius: 0.05
  sphereCol: {fileID: 2210462193945988030}
  sphereColWithRotScale: {x: 0, y: 0, z: 0}
  selfInteractionObject: {fileID: 0}
  selfInteractionEnabled: 0
  selectedByUI: 0
  checkOncePerObject: 0
  layerName: Player
  playerRigidbody: {fileID: 0}
  playerCollider: {fileID: 0}
  playerTransform: {fileID: 0}
  interactionStates: {fileID: 0}
  armLength: 0
  vehicleInput: {fileID: 0}
  vehiclePartCont: {fileID: 0}
  childTurrets: []
  vehiclePartsActive: 0
  lookAtTargetEnabled: 1
  waitForNewTarget: 0
  lookEndTimer: 0
  lookInitiateFailed: 0
  alternateHead: {fileID: 0}
  raycastDistance: 20
  debug: 1
  selectedTab: 0
  savePath: Assets/Interactor/SettelaInteractor.asset
  maxRadius: 0
  logoChange: 0
  opacity: 1
--- !u!114 &720600813861139802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2491691ef2f702a4598b3c2bac8d7109, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactor: {fileID: 2443512780624999051}
  animator: {fileID: 0}
  minHeight: 0
  midHeight: 1.2
  maxHeight: 2.2
  minDist: 0
  maxDist: 0.75
  maxMargin: 0.05
  crouchSetStart: 0.5
  initiated: 0
  clipPlaying: 0
  crouchWeightNormalized: 0
  debug: 1
  debugOrbitalPositioner: {fileID: 0}
  debugMirror: 0
  debugDuration: 0
  debugOrbitalValues: {x: 0, y: 0, z: 0}
--- !u!114 &2757235582597895822
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6430f5b64864c57459b325f15e863985, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animator: {fileID: 0}
  isHumanoid: 1
  ikParts:
  - part: 4
    matchChildBones: 1
    fixWristDeformation: 0
    excludeFromBones: []
    currentTarget: {fileID: 0}
    weight: 0
    rootBone: {fileID: 0}
    midBone: {fileID: 0}
    tipBone: {fileID: 0}
    hint: {fileID: 0}
    rootRotationOffset: 0
    midRotationOffset: 0
    targetDuration: 0
    backDuration: 0
    pause: 0
    waitForReset: 0
    boneTransform: {fileID: 0}
    enabled: 0
    interrupt: 0
    childBones: []
    weightedPosition: {x: 0, y: 0, z: 0}
    positionBeforeIK: {x: 0, y: 0, z: 0}
    rotationBeforeIK: {x: 0, y: 0, z: 0, w: 0}
  - part: 2
    matchChildBones: 1
    fixWristDeformation: 0
    excludeFromBones: []
    currentTarget: {fileID: 0}
    weight: 0
    rootBone: {fileID: 0}
    midBone: {fileID: 0}
    tipBone: {fileID: 0}
    hint: {fileID: 0}
    rootRotationOffset: 0
    midRotationOffset: 0
    targetDuration: 0
    backDuration: 0
    pause: 0
    waitForReset: 0
    boneTransform: {fileID: 0}
    enabled: 0
    interrupt: 0
    childBones: []
    weightedPosition: {x: 0, y: 0, z: 0}
    positionBeforeIK: {x: 0, y: 0, z: 0}
    rotationBeforeIK: {x: 0, y: 0, z: 0, w: 0}
  - part: 3
    matchChildBones: 1
    fixWristDeformation: 0
    excludeFromBones: []
    currentTarget: {fileID: 0}
    weight: 0
    rootBone: {fileID: 0}
    midBone: {fileID: 0}
    tipBone: {fileID: 0}
    hint: {fileID: 0}
    rootRotationOffset: 0
    midRotationOffset: 0
    targetDuration: 0
    backDuration: 0
    pause: 0
    waitForReset: 0
    boneTransform: {fileID: 0}
    enabled: 0
    interrupt: 0
    childBones: []
    weightedPosition: {x: 0, y: 0, z: 0}
    positionBeforeIK: {x: 0, y: 0, z: 0}
    rotationBeforeIK: {x: 0, y: 0, z: 0, w: 0}
  lookEnabled: 1
  lookTarget: {fileID: 0}
  lookWeight: 0
  headBone: {fileID: 0}
  lastHeadDirection: {x: 0, y: 0, z: 0}
--- !u!114 &7849422468994326215
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ce41d25b1d506af47a851432967add80, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactor: {fileID: 2443512780624999051}
  animator: {fileID: 0}
  animationClips:
  - {fileID: 7400000, guid: 77042cc464d967d46817dcca4dcadacf, type: 3}
  - {fileID: 7400000, guid: 7f162e3905b4b4b458678ab6cdaf1a20, type: 3}
  preventSpam: 1
--- !u!114 &9169839010926252671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a70e525c82ce9413fa4d940ad7fcf1db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  references:
    root: {fileID: 7507255572970579955}
    pelvis: {fileID: 0}
    leftThigh: {fileID: 0}
    leftCalf: {fileID: 0}
    leftFoot: {fileID: 0}
    rightThigh: {fileID: 0}
    rightCalf: {fileID: 0}
    rightFoot: {fileID: 0}
    leftUpperArm: {fileID: 0}
    leftForearm: {fileID: 0}
    leftHand: {fileID: 0}
    rightUpperArm: {fileID: 0}
    rightForearm: {fileID: 0}
    rightHand: {fileID: 0}
    head: {fileID: 0}
    spine:
    - {fileID: 0}
    - {fileID: 0}
    eyes: []
  solver:
    executedInEditor: 0
    IKPosition: {x: 0, y: 0, z: 0}
    IKPositionWeight: 1
    root: {fileID: 0}
    iterations: 4
    chain:
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 01000000020000000300000004000000
      childConstraints:
      - pushElasticity: 0
        pullElasticity: 1
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      - pushElasticity: 0
        pullElasticity: 1
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      - pushElasticity: 0
        pullElasticity: 0
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      - pushElasticity: 0
        pullElasticity: 0
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 1
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 1
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    effectors:
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones:
      - {fileID: 0}
      - {fileID: 0}
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    spineMapping:
      spineBones:
      - {fileID: 0}
      - {fileID: 0}
      - {fileID: 0}
      leftUpperArmBone: {fileID: 0}
      rightUpperArmBone: {fileID: 0}
      leftThighBone: {fileID: 0}
      rightThighBone: {fileID: 0}
      iterations: 3
      twistWeight: 1
    boneMappings:
    - bone: {fileID: 0}
      maintainRotationWeight: 0
    limbMappings:
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 0
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 0
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 1
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 1
      weight: 1
    FABRIKPass: 1
    rootNode: {fileID: 0}
    spineStiffness: 0.5
    pullBodyVertical: 0.5
    pullBodyHorizontal: 0
--- !u!114 &554600306175030877
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 309953741149805596}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 776adaaacdc5c4e8ab0395120a6e972b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 1.24, y: 1.1798275, z: 2.9983473}
    IKPositionWeight: 1
    root: {fileID: 7507255572970579955}
    target: {fileID: 0}
    spine:
    - transform: {fileID: 0}
      weight: 0
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.10808878, y: -0.008514151, z: -6.103008e-15}
      defaultLocalRotation: {x: -2.1942628e-24, y: -1.6274388e-23, z: 0.06238862,
        w: 0.99805194}
      length: 0
      sqrMag: 0
      axis: {x: -0.12815899, y: -0.9917538, z: -0.00000014901161}
      baseForwardOffsetEuler: {x: 0, y: 0, z: 0}
    head:
      transform: {fileID: 0}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0, y: 0, z: 0}
      defaultLocalRotation: {x: 0, y: -0, z: -0, w: 1}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 1}
      baseForwardOffsetEuler: {x: 0, y: 0, z: 0}
    eyes: []
    bodyWeight: 0.7
    headWeight: 1
    eyesWeight: 0
    clampWeight: 0.7
    clampWeightHead: 0.5
    clampWeightEyes: 0
    clampSmoothing: 0
    spineWeightCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.3
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    spineTargetOffset: {x: 0, y: 0, z: 0}
--- !u!1 &313523655128929462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 520379800261747354}
  m_Layer: 21
  m_Name: Pistol_00
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &520379800261747354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 313523655128929462}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11011008, y: -0.19348438, z: -0.7710402, w: 0.59660435}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1383474775041360180}
  - {fileID: 6938360488027384646}
  - {fileID: 3059993682524517977}
  - {fileID: 893050845294910504}
  - {fileID: 3484308106543394247}
  - {fileID: 3925261838396088709}
  - {fileID: 5284782164835845971}
  - {fileID: 7491053147720463444}
  - {fileID: 968039333386813128}
  - {fileID: 9038807045053803877}
  - {fileID: 7294275782284248493}
  m_Father: {fileID: 7471355865723362730}
  m_LocalEulerAnglesHint: {x: -9.613, y: -23.977, z: -102.491}
--- !u!1 &341615511917509030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8166452957145200494}
  m_Layer: 21
  m_Name: Aim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8166452957145200494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 341615511917509030}
  serializedVersion: 2
  m_LocalRotation: {x: -0.703968, y: -0.7074759, z: -0.040998396, w: 0.04718122}
  m_LocalPosition: {x: -0.016999971, y: 0.0030000117, z: 0.007999996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 938808586456395558}
  - {fileID: 7184852841583792328}
  m_Father: {fileID: 968039333386813128}
  m_LocalEulerAnglesHint: {x: -172.852, y: 0.5220032, z: -90.31702}
--- !u!1 &358447514292898397
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7047133969429612563}
  m_Layer: 0
  m_Name: CC_Base_Teeth02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7047133969429612563
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358447514292898397}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00022650788, z: -0.00009729427, w: -0.00008053859}
  m_LocalPosition: {x: -0.024823641, y: 0.012739562, z: 0.00014736093}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4207601964448435926}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &381808954779254968
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6442479970128640106}
  m_Layer: 0
  m_Name: index_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6442479970128640106
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 381808954779254968}
  serializedVersion: 2
  m_LocalRotation: {x: -0.015386808, y: -0.0002748661, z: -0.0053034103, w: 0.99986756}
  m_LocalPosition: {x: 0.00045135498, y: 0.028107528, z: -0.0007394695}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8007648465939198288}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &389592244943760014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1373038588085074837}
  - component: {fileID: 8969974200595079129}
  m_Layer: 0
  m_Name: ToKo_Underwear_Bra_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1373038588085074837
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 389592244943760014}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8969974200595079129
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 389592244943760014}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d013a4ba5c8aaa2458cdb9a205cd76fb, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7698357121477753718, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 7620355634370948199}
  - {fileID: 6354417209521280469}
  - {fileID: 7624248278982361176}
  - {fileID: 3316849888583807694}
  - {fileID: 1604281555635059624}
  - {fileID: 2912570544674203490}
  - {fileID: 4746873980746277075}
  - {fileID: 1570984535673114288}
  - {fileID: 4254692493541640014}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 7624248278982361176}
  m_AABB:
    m_Center: {x: -0.00050093234, y: 0.26455414, z: -0.025684223}
    m_Extent: {x: 0.16582833, y: 0.18954834, z: 0.17397897}
  m_DirtyAABB: 0
--- !u!1 &394368370531593377
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2252395986020352724}
  - component: {fileID: 9072846380482173134}
  m_Layer: 0
  m_Name: Sci_Fi_BGraph
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2252395986020352724
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 394368370531593377}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &9072846380482173134
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 394368370531593377}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 75b47edf762049149a922ff6504fef34, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7392520633234987534, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 6354417209521280469}
  - {fileID: 1570984535673114288}
  - {fileID: 2912570544674203490}
  - {fileID: 7620355634370948199}
  - {fileID: 1604281555635059624}
  - {fileID: 4746873980746277075}
  - {fileID: 4254692493541640014}
  - {fileID: 7624248278982361176}
  - {fileID: 3316849888583807694}
  - {fileID: 5009751097223364029}
  - {fileID: 3359683039249218388}
  - {fileID: 5383975135103560389}
  - {fileID: 7816194994385151601}
  - {fileID: 800051801862851842}
  - {fileID: 6703786042559524125}
  - {fileID: 1977733543387358763}
  - {fileID: 868485759455274028}
  - {fileID: 2682979858017989496}
  - {fileID: 6809340320723751594}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6809340320723751594}
  m_AABB:
    m_Center: {x: 0.0010021627, y: 0.30867955, z: 0.021864533}
    m_Extent: {x: 0.6461469, y: 0.24538127, z: 0.20483333}
  m_DirtyAABB: 0
--- !u!1 &492083702122930107
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 123275233525388870}
  - component: {fileID: 5786372545057745049}
  m_Layer: 7
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &123275233525388870
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 492083702122930107}
  serializedVersion: 2
  m_LocalRotation: {x: -0.8662188, y: -0.017015712, z: 0.0102342935, w: 0.49927026}
  m_LocalPosition: {x: -0.002756037, y: 0.2222864, z: 0.1136009}
  m_LocalScale: {x: 1.0000001, y: 1.0638297, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8451639263143781937}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &5786372545057745049
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 492083702122930107}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.11426404, y: 0.3016571, z: 0.11426404}
  m_Center: {x: -0.00312981, y: 0.16321746, z: -0.045183044}
--- !u!1 &527616573634828716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2898215844599901242}
  m_Layer: 0
  m_Name: ring_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2898215844599901242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 527616573634828716}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0009503501, y: 0.00013274279, z: -0.000009626135, w: 0.9999996}
  m_LocalPosition: {x: 0.00000061035155, y: 0.04171959, z: -0.00000071525574}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7269195448631105253}
  m_Father: {fileID: 4689124444566144165}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &563272365637637156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 318315931718969326}
  m_Layer: 0
  m_Name: thumb_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &318315931718969326
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 563272365637637156}
  serializedVersion: 2
  m_LocalRotation: {x: 0.3104095, y: -0.12205748, z: -0.096656166, w: 0.93776625}
  m_LocalPosition: {x: 0.008959808, y: 0.022829894, z: 0.018451862}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7556605754010412423}
  m_Father: {fileID: 6703786042559524125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &579940098129658588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 938808586456395558}
  - component: {fileID: 1825298027009553398}
  - component: {fileID: 5340030095267461038}
  m_Layer: 21
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &938808586456395558
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 579940098129658588}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8166452957145200494}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!33 &1825298027009553398
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 579940098129658588}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5340030095267461038
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 579940098129658588}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &584248744834951135
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2820369101759257821}
  m_Layer: 21
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2820369101759257821
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 584248744834951135}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4837934, y: 0.51108474, z: -0.4920204, w: 0.51249623}
  m_LocalPosition: {x: -0.025851786, y: 0.00839515, z: 0.0016169167}
  m_LocalScale: {x: 1.0152817, y: 1.0099486, z: 0.9751789}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2810497911127419708}
  m_Father: {fileID: 1410949846053669376}
  m_LocalEulerAnglesHint: {x: -108.337, y: -265.796, z: 272.1}
--- !u!1 &628671939561506607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9038807045053803877}
  m_Layer: 21
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9038807045053803877
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 628671939561506607}
  serializedVersion: 2
  m_LocalRotation: {x: 0.61545503, y: -0.47431195, z: -0.5169701, w: -0.35914516}
  m_LocalPosition: {x: 0.007, y: 0.045, z: 0.112}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &641109781992279798
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8472509196334568073}
  m_Layer: 0
  m_Name: InputAxisFloatHELPER
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8472509196334568073
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 641109781992279798}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &699008298068757780
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7184852841583792328}
  - component: {fileID: 4084109582350307050}
  - component: {fileID: 141071208885069794}
  m_Layer: 21
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7184852841583792328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 699008298068757780}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000007, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8166452957145200494}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!33 &4084109582350307050
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 699008298068757780}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &141071208885069794
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 699008298068757780}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &774251355539252186
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8007648465939198288}
  m_Layer: 0
  m_Name: index_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8007648465939198288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 774251355539252186}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000066359403, y: 0.005316494, z: -0.006927475, w: 0.9999619}
  m_LocalPosition: {x: -0.00000015258789, y: 0.038483657, z: 0.00000021457672}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6442479970128640106}
  m_Father: {fileID: 1027531031248273751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &896021565487074091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6603019320750421929}
  - component: {fileID: 8408529025615113783}
  - component: {fileID: 8767666081318376122}
  - component: {fileID: 8264305650820093507}
  m_Layer: 7
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6603019320750421929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896021565487074091}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000006556511, y: -0.0000038331455, z: -0.00001936654, w: 1}
  m_LocalPosition: {x: -0.0000029843027, y: 1.2180349, z: -0.03379741}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2939897042593437468}
  - {fileID: 7443338930424432770}
  - {fileID: 6411998766675819667}
  m_Father: {fileID: 4314299263516781743}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &8408529025615113783
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896021565487074091}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &8767666081318376122
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896021565487074091}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.23797359, y: 0.16854464, z: 0.14278415}
  m_Center: {x: 0.0000044474373, y: 0.028164845, z: 0.016374627}
--- !u!153 &8264305650820093507
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896021565487074091}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &908646693579108122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4614170868380316447}
  - component: {fileID: 6591187809807469422}
  m_Layer: 0
  m_Name: PistolState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4614170868380316447
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908646693579108122}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 164743807924787156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6591187809807469422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908646693579108122}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: be59ca64414230e4da448b03211d36ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: m_OnEndAnimationEvent
      Entry: 6
      Data: 
  m_aimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  _Character: {fileID: 2742023919547625688}
  WeaponGameObject: {fileID: 7895315661048287339}
  Equip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400150, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  UnEquip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400152, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  Idle:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 5bba91547eae2884f8b5638e2d2bd50d, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Aiming:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 636779f40299e34448c16e17b1d69899, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Shooting:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: a4de86a27114f8f438ae9e93e3be9836, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Reloading:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400096, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
--- !u!1 &938227931781302019
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4839714920747588693}
  m_Layer: 11
  m_Name: FloorAngleRaycaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4839714920747588693
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 938227931781302019}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.531, z: 0.373}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &938587511506730311
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3359683039249218388}
  m_Layer: 0
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3359683039249218388
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 938587511506730311}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000048121444, y: -0.000012135147, z: -0.004907392, w: 0.99998796}
  m_LocalPosition: {x: -0.00000061035155, y: 0.2813307, z: -0.000000009536743}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5383975135103560389}
  - {fileID: 5009751097223364029}
  m_Father: {fileID: 4746873980746277075}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &973440682403069401
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8392451243752310651}
  - component: {fileID: 8493923519519110048}
  m_Layer: 0
  m_Name: Ines_Boots_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8392451243752310651
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973440682403069401}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8493923519519110048
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973440682403069401}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a2bce4765e81d974bb67c6b0b904ff40, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2213655293627143353, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 2302558780381168062}
  - {fileID: 3696230167568432270}
  - {fileID: 5932891594977931567}
  - {fileID: 1771932099999606124}
  - {fileID: 3315732286828748650}
  - {fileID: 784631101705099529}
  - {fileID: 9184654208057938550}
  - {fileID: 7113810491443191821}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 784631101705099529}
  m_AABB:
    m_Center: {x: 0.07914308, y: 0.54150337, z: 0.0098627955}
    m_Extent: {x: 0.1488527, y: 0.14436929, z: 0.14782879}
  m_DirtyAABB: 0
--- !u!1 &1006973549881379124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3338510557000658300}
  m_Layer: 6
  m_Name: AimHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3338510557000658300
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1006973549881379124}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9116956559765925006}
  - {fileID: 4361150436108776979}
  - {fileID: 1087875349283475642}
  - {fileID: 1246005153757013277}
  - {fileID: 1821585893695261248}
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1090106775481054076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5269145932365844873}
  m_Layer: 0
  m_Name: index_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5269145932365844873
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1090106775481054076}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000016759956, y: -0.0053168475, z: 0.0070242267, w: 0.9999612}
  m_LocalPosition: {x: 0.00000015258789, y: 0.038484115, z: 0.00000030994414}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4245204652383740398}
  m_Father: {fileID: 4932665124173280898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1178495739168992854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3872495156044660684}
  - component: {fileID: 8950689789528778046}
  m_Layer: 0
  m_Name: MP4State
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3872495156044660684
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1178495739168992854}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 164743807924787156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8950689789528778046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1178495739168992854}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4a16dc15d3946288a9c9963dfcc76d2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: m_OnEndAnimationEvent
      Entry: 6
      Data: 
  m_aimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  _Character: {fileID: 2742023919547625688}
  WeaponGameObject: {fileID: 0}
  Equip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
  UnEquip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
  Idle:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 2eca9b0c87f01c843903863e6d00a38d, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Aiming:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: cb7af1a91c92e584697c05ddcffa8f85, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Shooting:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
  Reloading:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
--- !u!1 &1219460452532175012
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7624248278982361176}
  m_Layer: 0
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7624248278982361176
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1219460452532175012}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738, w: 0.99217784}
  m_LocalPosition: {x: 3.352761e-10, y: 0.038068235, z: 0.00000034332274}
  m_LocalScale: {x: 1.0000001, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6354417209521280469}
  m_Father: {fileID: 2682979858017989496}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1313842965755933435
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7795360010642506923}
  - component: {fileID: 431293079752128619}
  - component: {fileID: 5005547520356992618}
  m_Layer: 0
  m_Name: PlayerTrigger
  m_TagString: PlayerTrigger
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7795360010642506923
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313842965755933435}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &431293079752128619
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313842965755933435}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!135 &5005547520356992618
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313842965755933435}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.32
  m_Center: {x: 0, y: 0.25, z: 0}
--- !u!1 &1314421483186160965
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9116956559765925006}
  - component: {fileID: 385321489119878169}
  - component: {fileID: 214476561636164102}
  m_Layer: 6
  m_Name: AimIK Before Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9116956559765925006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314421483186160965}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3338510557000658300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &385321489119878169
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314421483186160965}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 4.8381405, y: 0.7027439, z: -0.28686422}
    IKPositionWeight: 0
    root: {fileID: 9116956559765925006}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 2682979858017989496}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
      defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268,
        w: 0.9867998}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 0}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: 2.197527, y: 0.8302772, z: 3.0314047}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!114 &214476561636164102
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314421483186160965}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: Only bones that used as muscle targets by PuppetMaster should ba added to
    AimIK's "Bones".
--- !u!1 &1453367817846328345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7126979179419134157}
  - component: {fileID: 7617596341067591016}
  m_Layer: 0
  m_Name: Ines_Body_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7126979179419134157
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1453367817846328345}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7617596341067591016
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1453367817846328345}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8de11d2c34af9584e98ad8fe854167f8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4083910945696752190, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 5932891594977931567}
  - {fileID: 3696230167568432270}
  - {fileID: 784631101705099529}
  - {fileID: 3315732286828748650}
  - {fileID: 2302558780381168062}
  - {fileID: 1771932099999606124}
  - {fileID: 4746873980746277075}
  - {fileID: 1977733543387358763}
  - {fileID: 6354417209521280469}
  - {fileID: 1604281555635059624}
  - {fileID: 1570984535673114288}
  - {fileID: 868485759455274028}
  - {fileID: 2912570544674203490}
  - {fileID: 5160767894998586803}
  - {fileID: 1246016646434924394}
  - {fileID: 6809340320723751594}
  - {fileID: 2312155369531029739}
  - {fileID: 3396170966632270810}
  - {fileID: 3359683039249218388}
  - {fileID: 800051801862851842}
  - {fileID: 4254692493541640014}
  - {fileID: 1975774117448653}
  - {fileID: 4207601964448435926}
  - {fileID: 3316849888583807694}
  - {fileID: 7624248278982361176}
  - {fileID: 7620355634370948199}
  - {fileID: 2682979858017989496}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6809340320723751594}
  m_AABB:
    m_Center: {x: 0.0009224564, y: -0.20033973, z: 0.015904918}
    m_Extent: {x: 0.43948817, y: 0.78844875, z: 0.21551071}
  m_DirtyAABB: 0
--- !u!1 &1562538961988508792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5009751097223364029}
  m_Layer: 0
  m_Name: lowerarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5009751097223364029
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1562538961988508792}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020261016, y: -0.0000023856562, z: -0.0000005890616,
    w: 1}
  m_LocalPosition: {x: 0.0000016784668, y: 0.12341133, z: -0.0000041913986}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3359683039249218388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1572235627386748248
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6776580594348942549}
  m_Layer: 0
  m_Name: thumb_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6776580594348942549
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1572235627386748248}
  serializedVersion: 2
  m_LocalRotation: {x: -0.045584954, y: -0.0017602224, z: -0.029903626, w: 0.99851125}
  m_LocalPosition: {x: -0, y: 0.026586989, z: 0.00000017166137}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1390068458671367855}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1614389917627834359
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3195304526723373558}
  m_Layer: 6
  m_Name: ThrowPos_LeftEnd
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3195304526723373558
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1614389917627834359}
  serializedVersion: 2
  m_LocalRotation: {x: -0.030620813, y: -0.78199154, z: -0.601753, w: -0.15951508}
  m_LocalPosition: {x: -0.22177395, y: 1.6594391, z: 0.4090307}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: -120.987, y: -50.890015, z: -125.175995}
--- !u!1 &1621750437938931908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1246005153757013277}
  m_Layer: 6
  m_Name: ForwardAimTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1246005153757013277
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1621750437938931908}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3338510557000658300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1685096421029076427
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3925261838396088709}
  m_Layer: 21
  m_Name: slide
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3925261838396088709
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1685096421029076427}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.053969655, z: -0.04202029}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4046584383438219079}
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1690804829183131458
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3049891311976575385}
  - component: {fileID: 2103078352562294230}
  m_Layer: 7
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3049891311976575385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690804829183131458}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86623275, y: 0.017648226, z: -0.010600417, w: 0.49921635}
  m_LocalPosition: {x: 0.0028650907, y: 0.22237192, z: 0.113568485}
  m_LocalScale: {x: 1, y: 1.0638298, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2641471771616559337}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &2103078352562294230
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690804829183131458}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.11426467, y: 0.30165872, z: 0.11426467}
  m_Center: {x: 0.0032451148, y: 0.16323803, z: -0.04520993}
--- !u!1 &1703090469266538935
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4421362856041145290}
  m_Layer: 0
  m_Name: GroundRaycaster2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4421362856041145290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703090469266538935}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.00000018618678, z: 0, w: 1}
  m_LocalPosition: {x: 0.000000018157792, y: 0, z: 0.19933805}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2555468018400716107}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1751589050838256703
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2810497911127419708}
  m_Layer: 21
  m_Name: Aim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2810497911127419708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751589050838256703}
  serializedVersion: 2
  m_LocalRotation: {x: -0.703968, y: -0.7074759, z: -0.040998396, w: 0.04718122}
  m_LocalPosition: {x: -0.016999971, y: 0.0030000117, z: 0.007999996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2199128037404243051}
  - {fileID: 4841403013678523602}
  m_Father: {fileID: 2820369101759257821}
  m_LocalEulerAnglesHint: {x: -172.852, y: 0.5220032, z: -90.31702}
--- !u!1 &1860265234321281053
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2939897042593437468}
  - component: {fileID: 3072860972693961207}
  - component: {fileID: 7886730735077939540}
  - component: {fileID: 5919505294671486721}
  m_Layer: 7
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2939897042593437468
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860265234321281053}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00025808235, y: 0.00028837987, z: 0.99977154, w: -0.021374444}
  m_LocalPosition: {x: -0.09741039, y: -0.022850707, z: -0.0022066818}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6393905998216405920}
  m_Father: {fileID: 6603019320750421929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3072860972693961207
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860265234321281053}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &7886730735077939540
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860265234321281053}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.10345325
  m_Height: 0.50577146
  m_Direction: 1
  m_Center: {x: -0.0000002859161, y: 0.23964992, z: -0.00000034327965}
--- !u!153 &5919505294671486721
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860265234321281053}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8408529025615113783}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1871414843158598575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7620355634370948199}
  m_Layer: 0
  m_Name: CC_Base_R_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7620355634370948199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1871414843158598575}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0010908963, y: 0.6153887, z: 0.78822196, w: -0.0013186043}
  m_LocalPosition: {x: 0.09821229, y: 0.047898404, z: 0.14247963}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6354417209521280469}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1908376218138474289
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4932665124173280898}
  m_Layer: 0
  m_Name: index_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4932665124173280898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1908376218138474289}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07301684, y: 0.0035579472, z: 0.040607445, w: 0.99649733}
  m_LocalPosition: {x: -0.0016212463, y: 0.095081024, z: 0.017892342}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5269145932365844873}
  m_Father: {fileID: 5383975135103560389}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1941029044645847218
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3737002327134446675}
  m_Layer: 0
  m_Name: GroundRaycaster3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3737002327134446675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1941029044645847218}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.0000001860467, z: 0, w: 1}
  m_LocalPosition: {x: -0.00000016485335, y: 0, z: -0.26091012}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2555468018400716107}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1991192367108055948
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1812205661720675985}
  m_Layer: 0
  m_Name: ring_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1812205661720675985
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991192367108055948}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0036970049, y: -0.0002580989, z: -0.000032584452, w: 0.99999315}
  m_LocalPosition: {x: 0.00000015258789, y: 0.042684708, z: -0.000000009536743}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8322871946417185695}
  m_Father: {fileID: 3736384074916615092}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2022201242211564416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1383474775041360180}
  m_Layer: 21
  m_Name: body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1383474775041360180
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2022201242211564416}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2049830935794145016
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 784631101705099529}
  m_Layer: 0
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &784631101705099529
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2049830935794145016}
  serializedVersion: 2
  m_LocalRotation: {x: -0.005145941, y: -0.0001241103, z: 0.024217186, w: 0.99969345}
  m_LocalPosition: {x: 0.00000020980835, y: 0.4792871, z: -0.00000022649765}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3315732286828748650}
  - {fileID: 1771932099999606124}
  m_Father: {fileID: 3396170966632270810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2065613332174818168
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 151253809231129524}
  - component: {fileID: 757759300228172978}
  - component: {fileID: 6011380008943947606}
  - component: {fileID: 1154010614921575652}
  m_Layer: 7
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &151253809231129524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2065613332174818168}
  serializedVersion: 2
  m_LocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
  m_LocalPosition: {x: 0.000025579702, y: 0.24901603, z: 0.00000015788804}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1354757304456323966}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &757759300228172978
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2065613332174818168}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &6011380008943947606
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2065613332174818168}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07146499
  m_Height: 0.20963065
  m_Direction: 1
  m_Center: {x: -0.0000004889444, y: 0.09339512, z: -0.0000000018626454}
--- !u!153 &1154010614921575652
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2065613332174818168}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5245768833647091730}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2117695818091647871
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9078335296355087865}
  m_Layer: 0
  m_Name: SubModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9078335296355087865
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117695818091647871}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6288012920567654403}
  - {fileID: 8463786004102510515}
  m_Father: {fileID: 5729056741835728292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2180734942754366178
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3736384074916615092}
  m_Layer: 0
  m_Name: ring_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3736384074916615092
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2180734942754366178}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07828313, y: 0.00031734316, z: -0.043870766, w: 0.99596536}
  m_LocalPosition: {x: 0.0020114135, y: 0.087606505, z: -0.015832042}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1812205661720675985}
  m_Father: {fileID: 6703786042559524125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2198871393161281857
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2528320946427295909}
  - component: {fileID: 5789275642811653956}
  - component: {fileID: 2643143733105868376}
  - component: {fileID: 5419504423133491858}
  m_Layer: 7
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2528320946427295909
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2198871393161281857}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02410228, y: -0.028923262, z: -0.7123166, w: 0.7008478}
  m_LocalPosition: {x: 0.1461604, y: 0.35195455, z: -0.08946073}
  m_LocalScale: {x: 0.99999994, y: 0.9999996, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1354757304456323966}
  m_Father: {fileID: 6411998766675819667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5789275642811653956
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2198871393161281857}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &2643143733105868376
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2198871393161281857}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.086146735
  m_Height: 0.31587136
  m_Direction: 1
  m_Center: {x: 0.000000045634817, y: 0.14070855, z: 0.0000004023315}
--- !u!153 &5419504423133491858
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2198871393161281857}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 511570501894979341}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2227744085375286098
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4746873980746277075}
  m_Layer: 0
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4746873980746277075
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2227744085375286098}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12092967, y: 0.0028766154, z: 0.029973214, w: 0.9922043}
  m_LocalPosition: {x: 0.00000015258789, y: 0.10289588, z: 0.0000000047683715}
  m_LocalScale: {x: 0.99999976, y: 0.9999998, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3359683039249218388}
  - {fileID: 1977733543387358763}
  m_Father: {fileID: 1604281555635059624}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2258269944555405004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7969376479293448957}
  - component: {fileID: 1853665388240984892}
  m_Layer: 21
  m_Name: mag 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7969376479293448957
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2258269944555405004}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 893050845294910504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1853665388240984892
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2258269944555405004}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7371548420364037637, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 1383474775041360180}
  - {fileID: 3059993682524517977}
  - {fileID: 3925261838396088709}
  - {fileID: 4046584383438219079}
  - {fileID: 5284782164835845971}
  - {fileID: 6938360488027384646}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3925261838396088709}
  m_AABB:
    m_Center: {x: 0, y: -0.08626072, z: -0.014301309}
    m_Extent: {x: 0.014163713, y: 0.059781164, z: 0.026841497}
  m_DirtyAABB: 0
--- !u!1 &2291211333532282749
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7269195448631105253}
  m_Layer: 0
  m_Name: ring_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7269195448631105253
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2291211333532282749}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00081478525, y: 0.00009944815, z: 0.022980655, w: 0.9997356}
  m_LocalPosition: {x: -0.00000045776366, y: 0.026329346, z: 0.000000038146972}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2898215844599901242}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2342314843269504864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4214021402500490547}
  - component: {fileID: 3410757276731155513}
  - component: {fileID: 8433626784413808527}
  - component: {fileID: 677984794157596908}
  m_Layer: 7
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4214021402500490547
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2342314843269504864}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000048987545, y: -0.000012127677, z: -0.004907473, w: 0.99998796}
  m_LocalPosition: {x: -0.00026976556, y: 0.28133482, z: -0.00000011103114}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5126652035915794677}
  m_Father: {fileID: 3587060510133478845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3410757276731155513
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2342314843269504864}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &8433626784413808527
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2342314843269504864}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.077508435
  m_Height: 0.27922314
  m_Direction: 1
  m_Center: {x: 0.0000005689216, y: 0.12438155, z: -0.00000009420073}
--- !u!153 &677984794157596908
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2342314843269504864}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 2506931397602712867}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.5983981
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.4016
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2455933999946724618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2641471771616559337}
  - component: {fileID: 5486519470322199624}
  - component: {fileID: 6249573664992107028}
  m_Layer: 7
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2641471771616559337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2455933999946724618}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213843, y: -0.07327937, z: 0.035943985, w: 0.9615725}
  m_LocalPosition: {x: 0.0001173988, y: 0.525276, z: 0.00031326807}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3049891311976575385}
  m_Father: {fileID: 7892479749728525563}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5486519470322199624
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2455933999946724618}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &6249573664992107028
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2455933999946724618}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8931926817661797498}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2492652169950576623
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1613259984349477879}
  m_Layer: 6
  m_Name: Inventory1Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1613259984349477879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2492652169950576623}
  serializedVersion: 2
  m_LocalRotation: {x: -0.13387737, y: -0.18126856, z: 0.91948843, w: -0.32211727}
  m_LocalPosition: {x: -0.0001, y: 1.0819793, z: -0.136}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1056637287934946271}
  m_LocalEulerAnglesHint: {x: 24.809002, y: -8.1970005, z: 216.80699}
--- !u!1 &2521049830816500809
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2995145027097432860}
  - component: {fileID: 4096809560676780992}
  - component: {fileID: 6776305550790188624}
  m_Layer: 2
  m_Name: WallDetector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2995145027097432860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2521049830816500809}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.301, z: 0.568}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &4096809560676780992
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2521049830816500809}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.38
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &6776305550790188624
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2521049830816500809}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &2693836428415592916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6449606165044503461}
  - component: {fileID: 1053416259420347855}
  m_Layer: 0
  m_Name: CC_Base_Teeth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6449606165044503461
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2693836428415592916}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1053416259420347855
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2693836428415592916}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: abfb25e4f694953438ae403c6e9d6024, type: 2}
  - {fileID: 2100000, guid: 6fa55045d8ab1c147a17e89a9c56285d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5727283436533813174, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 7047133969429612563}
  - {fileID: 1256160826407134159}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1256160826407134159}
  m_AABB:
    m_Center: {x: -0.001902123, y: -0.018704455, z: 0.000000009313226}
    m_Extent: {x: 0.024316978, y: 0.01927523, z: 0.023962753}
  m_DirtyAABB: 0
--- !u!1 &2727808161665621033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5126652035915794677}
  - component: {fileID: 3609051884866679598}
  - component: {fileID: 4473978991007477735}
  - component: {fileID: 3957457229971227730}
  m_Layer: 7
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5126652035915794677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2727808161665621033}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
  m_LocalPosition: {x: -0.00008228723, y: 0.24876341, z: -0.000000034562728}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4214021402500490547}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3609051884866679598
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2727808161665621033}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &4473978991007477735
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2727808161665621033}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0713923
  m_Height: 0.2094174
  m_Direction: 1
  m_Center: {x: 0.0000005546026, y: 0.09330003, z: -0.00000007078051}
--- !u!153 &3957457229971227730
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2727808161665621033}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3410757276731155513}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2746218843486250994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4361150436108776979}
  - component: {fileID: 6820971574158843217}
  m_Layer: 6
  m_Name: AimIK After Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4361150436108776979
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2746218843486250994}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3338510557000658300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6820971574158843217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2746218843486250994}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 7.4485736, y: 0.3600001, z: 6.7926}
    IKPositionWeight: 1
    root: {fileID: 4361150436108776979}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 6
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 2682979858017989496}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
      defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268,
        w: 0.9867998}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 7624248278982361176}
      weight: 0.721
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 3.352761e-10, y: 0.038068235, z: 0.00000034332274}
      defaultLocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738,
        w: 0.99217784}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 4254692493541640014}
      weight: 0.433
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.0000000077486035, y: 0.26940826, z: -0.0000020599364}
      defaultLocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 2820369101759257821}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: 2.197527, y: 0.8302772, z: 3.0314047}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &2895351526082784395
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5729056741835728292}
  - component: {fileID: 7029741597687228658}
  - component: {fileID: 3042903506368811451}
  m_Layer: 0
  m_Name: AimingModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5729056741835728292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2895351526082784395}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 991082308300171921}
  - {fileID: 9078335296355087865}
  m_Father: {fileID: 4730494400458778153}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7029741597687228658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2895351526082784395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0dcbffa32d899b4cac41b83a61cef7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 7142898169479841738}
    - {fileID: 969609528223833965}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <SubModules>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[WeaponSubModuleState, General],[SubModule.Aiming.AimingSubModule,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubModuleState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 1
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 0
    - Name: $v
      Entry: 10
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  <CanUpdate>k__BackingField: 0
  <ControllerIndex>k__BackingField: 0
  mainState: 000000000100000002000000
  subState: 0
  <SubModuleState>k__BackingField: 1
  <InputName>k__BackingField: 0300000004000000
  hasRefreshRate: 0
  refreshRate: 0
  executionPriority: 0
  m_lookAtIK: {fileID: 554600306175030877}
  ragdollAiming: {fileID: 3042903506368811451}
  autoAiming: 0
  aimingConfig: {fileID: 11400000, guid: a05d9d69b4b2427685614e4622e177c7, type: 2}
  currentConeAngle: 0
  coneRange: 6
  playerTransform: {fileID: 1768001276132255913}
  aimHelperTransform: {fileID: 7332317681643830073}
  m_isForwardAiming: 0
  m_aimTarget: {fileID: 1821585893695261248}
  m_forwardAimTarget: {fileID: 1246005153757013277}
  m_targetDetected: 0
  m_targetName: 
  m_lastTarget:
    x: 0
    y: 0
    z: 0
  m_isInFOV: 0
  m_currentScore: 0
  m_detectSensorBase: {fileID: 7286677389078680567}
  m_canSwitchTarget: 1
  m_characterParameters: {fileID: 3336923530583100265}
  m_closestTargetDistanceThreshold: 0.01
  m_isAnyTargetBehind: 0
  _cooldownEndTime: -1
  m_cachedDetectedEnemies: []
  m_cachedCurrentTargetPosition:
    x: 0
    y: 0
    z: 0
  m_hasCachedTarget: 0
--- !u!114 &3042903506368811451
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2895351526082784395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a9db18cd58f72348905fc8ae0a482d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 5862402671225516785}
  aimIKBeforePhysics: {fileID: 385321489119878169}
  target: {fileID: 1821585893695261248}
  fixAiming: 1
  fixLeftHand: 1
  aimIKAfterPhysics: {fileID: 6820971574158843217}
  hasLeftHandIK: 0
  leftHandIK: {fileID: 6933222966420822129}
  leftHandTarget: {fileID: 5383975135103560389}
  weight: 0
  targetSwitchSmoothTime: 0.3
  weightSmoothTime: 0.3
  smoothTurnTowardsTarget: 1
  maxRadiansDelta: 3
  maxMagnitudeDelta: 3
  slerpSpeed: 3
  smoothDampTime: 0
  pivotOffsetFromRoot: {x: 0, y: 1, z: 0}
  minDistance: 1
  offset: {x: 0, y: 0, z: 0}
  maxRootAngle: 45
  turnToTarget: 0
  turnToTargetTime: 0.2
  useAnimatedAimDirection: 0
  animatedAimDirection: {x: 0, y: 0, z: 1}
  m_currentAimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  animator: {fileID: 0}
--- !u!1 &2925430643129237699
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1390068458671367855}
  m_Layer: 0
  m_Name: thumb_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1390068458671367855
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2925430643129237699}
  serializedVersion: 2
  m_LocalRotation: {x: -0.1623765, y: 0.004189572, z: -0.09236491, w: 0.9823874}
  m_LocalPosition: {x: 0.0000007629394, y: 0.054367825, z: -0.0000007724762}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6776580594348942549}
  m_Father: {fileID: 7839827620740900589}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3002470268733502933
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6393905998216405920}
  - component: {fileID: 8714427012979128679}
  - component: {fileID: 302814639558263754}
  - component: {fileID: 7577523255667450435}
  m_Layer: 7
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6393905998216405920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3002470268733502933}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00548152, y: 0.00012282522, z: -0.022256006, w: 0.99973726}
  m_LocalPosition: {x: 0.0013046343, y: 0.50983846, z: -0.000018496326}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8451639263143781937}
  m_Father: {fileID: 2939897042593437468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &8714427012979128679
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3002470268733502933}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &302814639558263754
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3002470268733502933}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09310791
  m_Height: 0.5209091
  m_Direction: 1
  m_Center: {x: 0.0000002207234, y: 0.24685049, z: -0.00000030291264}
--- !u!153 &7577523255667450435
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3002470268733502933}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3072860972693961207}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.7928116
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.20718
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3201995294337515075
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8322871946417185695}
  m_Layer: 0
  m_Name: ring_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8322871946417185695
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3201995294337515075}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0008003152, y: -0.000099235, z: -0.023055706, w: 0.99973387}
  m_LocalPosition: {x: -0.0000015258788, y: 0.027640838, z: -0.0000021362305}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1812205661720675985}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3239685838274332452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4127082261127725367}
  m_Layer: 0
  m_Name: pinky_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4127082261127725367
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3239685838274332452}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0070864474, y: 0.0012980792, z: 0.00002894654, w: 0.9999741}
  m_LocalPosition: {x: 0.00000030517577, y: 0.033087693, z: -0.000000019073486}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2152997211138788668}
  m_Father: {fileID: 8657857344066895426}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3325704005847820277
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2102168643567736953}
  m_Layer: 0
  m_Name: CC_Base_Tongue02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2102168643567736953
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3325704005847820277}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0011598348, y: -0.005633796, z: -0.09942047, w: 0.9950289}
  m_LocalPosition: {x: -0.009785385, y: 0.0002241516, z: -0.0000043535233}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4778733340781212292}
  m_Father: {fileID: 6332157594019442039}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3381988054035651874
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2673969226716299816}
  m_Layer: 0
  m_Name: InteractionCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2673969226716299816
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3381988054035651874}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3390300180486917183
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5284782164835845971}
  m_Layer: 21
  m_Name: trigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5284782164835845971
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3390300180486917183}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.015382193, z: -0.0020682926}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3440880463034578170
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7816194994385151601}
  m_Layer: 0
  m_Name: lowerarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7816194994385151601
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3440880463034578170}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020659559, y: -0.0000015906267, z: 0.00000052282104,
    w: 1}
  m_LocalPosition: {x: -0, y: 0.12450798, z: 0.00000007152557}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 800051801862851842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3457568820817922553
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7323052689804063343}
  m_Layer: 0
  m_Name: middle_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7323052689804063343
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3457568820817922553}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00771825, y: -0.00016319613, z: -0.021914285, w: 0.9997301}
  m_LocalPosition: {x: -0.000027160644, y: 0.028993454, z: 0.0000051403044}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3975190985635198000}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3471632826789420921
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 164743807924787156}
  - component: {fileID: 792671241793180101}
  - component: {fileID: 2742023919547625688}
  m_Layer: 0
  m_Name: WeaponModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &164743807924787156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3471632826789420921}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3872495156044660684}
  - {fileID: 4614170868380316447}
  m_Father: {fileID: 4730494400458778153}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &792671241793180101
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3471632826789420921}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 88226de0bc8347118b6651808977535d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 5572294579708353132}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <SubModules>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[WeaponSubModuleState, General],[SubModule.Weapon.WeaponSubModule,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubModuleState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 0
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  <ControllerIndex>k__BackingField: 0
  m_weaponIndex: 0
  <MainState>k__BackingField: 000000000100000002000000
  _subModuleState: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  animationModule: {fileID: 2742023919547625688}
  m_CurrentSubModuleState: {fileID: 0}
  <InputName>k__BackingField: 1200000013000000030000001600000017000000
  _weaponSubState: 0
  m_isShooting: 0
  m_isAiming: 0
  m_autoShooting: 0
  m_lastShootingState: 0
  m_detectedTarget: 0
  <CurrentBulletType>k__BackingField: 0
  BulletPrefab: {fileID: 1218724716591446, guid: 0e2eba3d3c3303446ab3f67d8a25d6b3,
    type: 3}
  BulletPrefabs: []
  bulletTypeData: {fileID: 11400000, guid: 87db5caf701a66740a65c07309226b52, type: 2}
  bulletSpeed: 0
  spreadMultiplier: 1
--- !u!114 &2742023919547625688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3471632826789420921}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8691dd0ae36145d88c17f855c2eb91ba, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 8950689789528778046}
    - {fileID: 6591187809807469422}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <States>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.Enum, mscorlib],[Module.Mono.Animancer.RealsticFemale.WeaponState,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.ObjectEqualityComparer`1[[System.Enum, mscorlib]],
        mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 2|WeaponSubModuleState, General
    - Name: 
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 3|WeaponSubModuleState, General
    - Name: 
      Entry: 3
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  m_useAnimancer: 0
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 000000000100000002000000
  <SubState>k__BackingField: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  _Animancer: {fileID: 712441798001151660}
  _Parameters: {fileID: 3336923530583100265}
  unifiedLayeredAnimationAdapter: {fileID: 2194881769724018296}
  _subModuleState: 0
  m_autoAiming: 0
  m_detectTarget: 0
  <InputName>k__BackingField: 040000000300000013000000
  <State>k__BackingField: 0
--- !u!1 &3521077284355750587
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8478060350893148715}
  m_Layer: 21
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8478060350893148715
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3521077284355750587}
  serializedVersion: 2
  m_LocalRotation: {x: 0.77128386, y: -0.026550846, z: 0.0323993, w: -0.6351116}
  m_LocalPosition: {x: -0.13594225, y: -0.02086684, z: 0.004009595}
  m_LocalScale: {x: 1.010579, y: 1.0150763, z: 0.9747495}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1410949846053669376}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3589128975472779037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6703786042559524125}
  m_Layer: 0
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6703786042559524125
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3589128975472779037}
  serializedVersion: 2
  m_LocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
  m_LocalPosition: {x: 0.00000015258789, y: 0.24901588, z: 0.0000001335144}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1027531031248273751}
  - {fileID: 1316463068385753957}
  - {fileID: 6722320523965547818}
  - {fileID: 3736384074916615092}
  - {fileID: 318315931718969326}
  - {fileID: 7471355865723362730}
  m_Father: {fileID: 800051801862851842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3649173763041561515
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1056637287934946271}
  - component: {fileID: 5350220601721065077}
  m_Layer: 6
  m_Name: Inventory1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1056637287934946271
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3649173763041561515}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1613259984349477879}
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5350220601721065077
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3649173763041561515}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 54f66acbd9188584690b838d9470889d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  currentInteractor: {fileID: 0}
  inventoryName: Inventory1
  inventorySize: {x: 4, y: 3}
  inventoryPoint: {fileID: 1613259984349477879}
  invOnBodyPoints:
  - bodyPoint: {fileID: 1613259984349477879}
    bodyPointEnd: {fileID: 1613259984349477879}
    endSpeedMult: 0
    currentObject: {fileID: 0}
  - bodyPoint: {fileID: 0}
    bodyPointEnd: {fileID: 0}
    endSpeedMult: 0
    currentObject: {fileID: 0}
  changeOwner: {fileID: 0}
  addItem: {fileID: 0}
  removeItem: {fileID: 0}
  reorderInventory: 0
  currentItems:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  currentAmounts: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
  invUpdated: 0
  reordered: 0
--- !u!1 &3674661703824157641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4730494400458778153}
  m_Layer: 0
  m_Name: Modules
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4730494400458778153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3674661703824157641}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6862282147661311009}
  - {fileID: 4872260779970431100}
  - {fileID: 5729056741835728292}
  - {fileID: 7507255572970579955}
  - {fileID: 164743807924787156}
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3716751332337835497
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7332317681643830073}
  m_Layer: 6
  m_Name: AimingTransformHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7332317681643830073
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3716751332337835497}
  serializedVersion: 2
  m_LocalRotation: {x: -0.45451945, y: 0.54167527, z: -0.45451945, w: -0.54167527}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4254692493541640014}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3745009443806157409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 478716012185291693}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &478716012185291693
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3745009443806157409}
  serializedVersion: 2
  m_LocalRotation: {x: -0.8662188, y: -0.017015712, z: 0.0102342935, w: 0.49927026}
  m_LocalPosition: {x: -0.002756037, y: 0.2222864, z: 0.1136009}
  m_LocalScale: {x: 1.0000001, y: 1.0638297, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2302558780381168062}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3783199281384461274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8261531813573869650}
  m_Layer: 0
  m_Name: GroundRaycaster5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8261531813573869650
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3783199281384461274}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.0000001860467, z: 0, w: 1}
  m_LocalPosition: {x: 0.1877264, y: 0, z: -0.00000011395527}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2555468018400716107}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3801287200198421456
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4357931742740439138}
  m_Layer: 21
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4357931742740439138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3801287200198421456}
  serializedVersion: 2
  m_LocalRotation: {x: 0.25672767, y: -0.010788244, z: 0.79459333, w: -0.5500874}
  m_LocalPosition: {x: 0.028688993, y: -0.09583068, z: -0.004698406}
  m_LocalScale: {x: 0.9793076, y: 1.0065103, z: 1.0144148}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1410949846053669376}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &3883336041726506854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 117205096757043403}
  - component: {fileID: 643832875073745834}
  m_Layer: 0
  m_Name: CC_Base_Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &117205096757043403
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3883336041726506854}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &643832875073745834
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3883336041726506854}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 231f293b4d8f36f46bf37186c629de1e, type: 2}
  - {fileID: 2100000, guid: ed2b5a2049d359c4bbf89fd1847dddb0, type: 2}
  - {fileID: 2100000, guid: 2ad698a8dad45fc4a8d37c31b58dcdf5, type: 2}
  - {fileID: 2100000, guid: ee7c162b70ff995438710e2235472f8c, type: 2}
  - {fileID: 2100000, guid: 7cdfc14f5abeb8d4d93f1cc553af6919, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2988033852023935963, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 3359683039249218388}
  - {fileID: 5009751097223364029}
  - {fileID: 6809340320723751594}
  - {fileID: 7624248278982361176}
  - {fileID: 2682979858017989496}
  - {fileID: 6354417209521280469}
  - {fileID: 1975774117448653}
  - {fileID: 4207601964448435926}
  - {fileID: 4254692493541640014}
  - {fileID: 1246016646434924394}
  - {fileID: 3396170966632270810}
  - {fileID: 1977733543387358763}
  - {fileID: 4746873980746277075}
  - {fileID: 5932891594977931567}
  - {fileID: 3696230167568432270}
  - {fileID: 2302558780381168062}
  - {fileID: 5160767894998586803}
  - {fileID: 5383975135103560389}
  - {fileID: 1143483047653081985}
  - {fileID: 7272893972138136223}
  - {fileID: 4932665124173280898}
  - {fileID: 5730334259293684896}
  - {fileID: 4689124444566144165}
  - {fileID: 5269145932365844873}
  - {fileID: 4245204652383740398}
  - {fileID: 2898215844599901242}
  - {fileID: 7269195448631105253}
  - {fileID: 8657857344066895426}
  - {fileID: 4127082261127725367}
  - {fileID: 2152997211138788668}
  - {fileID: 7839827620740900589}
  - {fileID: 1390068458671367855}
  - {fileID: 6776580594348942549}
  - {fileID: 9184654208057938550}
  - {fileID: 1604281555635059624}
  - {fileID: 3316849888583807694}
  - {fileID: 7620355634370948199}
  - {fileID: 2912570544674203490}
  - {fileID: 800051801862851842}
  - {fileID: 7816194994385151601}
  - {fileID: 868485759455274028}
  - {fileID: 1570984535673114288}
  - {fileID: 784631101705099529}
  - {fileID: 3315732286828748650}
  - {fileID: 1771932099999606124}
  - {fileID: 2312155369531029739}
  - {fileID: 1316463068385753957}
  - {fileID: 3975190985635198000}
  - {fileID: 6703786042559524125}
  - {fileID: 1027531031248273751}
  - {fileID: 7323052689804063343}
  - {fileID: 3736384074916615092}
  - {fileID: 8007648465939198288}
  - {fileID: 6442479970128640106}
  - {fileID: 1812205661720675985}
  - {fileID: 8322871946417185695}
  - {fileID: 6722320523965547818}
  - {fileID: 6783681568393303306}
  - {fileID: 8061476685868375142}
  - {fileID: 318315931718969326}
  - {fileID: 7556605754010412423}
  - {fileID: 6986818677904699797}
  - {fileID: 7113810491443191821}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 6809340320723751594}
  m_AABB:
    m_Center: {x: 0.00039353967, y: 0.5694388, z: 0.004483696}
    m_Extent: {x: 0.8668691, y: 0.162474, z: 0.102908105}
  m_DirtyAABB: 0
--- !u!1 &3941623835834854544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6722320523965547818}
  m_Layer: 0
  m_Name: pinky_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6722320523965547818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3941623835834854544}
  serializedVersion: 2
  m_LocalRotation: {x: -0.08363916, y: -0.00074685307, z: -0.057137705, w: 0.9948564}
  m_LocalPosition: {x: 0.005911102, y: 0.08264572, z: -0.030138053}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6783681568393303306}
  m_Father: {fileID: 6703786042559524125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3995989583001055879
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6249227604688449147}
  - component: {fileID: 1817442324615788480}
  m_Layer: 0
  m_Name: CC_Base_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6249227604688449147
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3995989583001055879}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1817442324615788480
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3995989583001055879}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4372044fa75068540a70e02fce99869d, type: 2}
  - {fileID: 2100000, guid: 9eb706bdb64514d40aebd42dff2b7421, type: 2}
  - {fileID: 2100000, guid: a74d138b42e43c34aa7d8acec14b25f8, type: 2}
  - {fileID: 2100000, guid: e4995a92ac6380b4aa7bd9e587bdcee4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7654024864961597166, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 7309246504749364867}
  - {fileID: 5695610274983953874}
  m_BlendShapeWeights:
  - 0
  - 0
  m_RootBone: {fileID: 5695610274983953874}
  m_AABB:
    m_Center: {x: -0.031884596, y: -0.00096217636, z: -0.00013517216}
    m_Extent: {x: 0.048445284, y: 0.01696821, z: 0.01673663}
  m_DirtyAABB: 0
--- !u!1 &4041911241137123833
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7443338930424432770}
  - component: {fileID: 2044566165639447891}
  - component: {fileID: 5843829859340073522}
  - component: {fileID: 8062682008002147427}
  m_Layer: 7
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7443338930424432770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4041911241137123833}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00026941192, y: 0.00016035745, z: 0.9997506, w: 0.022329321}
  m_LocalPosition: {x: 0.09741145, y: -0.022797264, z: -0.0022248642}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7892479749728525563}
  m_Father: {fileID: 6603019320750421929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &2044566165639447891
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4041911241137123833}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &5843829859340073522
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4041911241137123833}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.103451595
  m_Height: 0.5057633
  m_Direction: 1
  m_Center: {x: 0.00000010896474, y: 0.23964335, z: -0.00000005843321}
--- !u!153 &8062682008002147427
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4041911241137123833}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8408529025615113783}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &4118426930663282399
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5383975135103560389}
  m_Layer: 0
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5383975135103560389
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4118426930663282399}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
  m_LocalPosition: {x: 0.00000015258789, y: 0.24876311, z: -0.000000038146972}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4932665124173280898}
  - {fileID: 1143483047653081985}
  - {fileID: 8657857344066895426}
  - {fileID: 4689124444566144165}
  - {fileID: 7839827620740900589}
  m_Father: {fileID: 3359683039249218388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4147841900750799876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8657857344066895426}
  m_Layer: 0
  m_Name: pinky_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8657857344066895426
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4147841900750799876}
  serializedVersion: 2
  m_LocalRotation: {x: -0.08479612, y: 0.00079936325, z: 0.057274908, w: 0.9947505}
  m_LocalPosition: {x: -0.005909271, y: 0.0826802, z: -0.030114098}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4127082261127725367}
  m_Father: {fileID: 5383975135103560389}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4395314115312168613
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 13362772404359782}
  - component: {fileID: 2309955415401863828}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &13362772404359782
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4395314115312168613}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8884557858176304778}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2309955415401863828
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4395314115312168613}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1efacf79ab4214e85aeebfd07064c20f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 0}
  deactivated: 0
  masterProps:
    normalMode: 0
    mappingBlendSpeed: 10
    activateOnStaticCollisions: 0
    activateOnImpulse: 0
  groundLayers:
    serializedVersion: 2
    m_Bits: 16
  collisionLayers:
    serializedVersion: 2
    m_Bits: 1
  collisionThreshold: 0
  collisionResistance:
    mode: 0
    floatValue: 25
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    tooltip: Smaller value means more unpinning from collisions so the characters
      get knocked out more easily. If using a curve, the value will be evaluated
      by each muscle's target velocity magnitude. This can be used to make collision
      resistance higher while the character moves or animates faster.
  collisionResistanceMultipliers: []
  maxCollisions: 1
  regainPinSpeed: 1.5
  boostFalloff: 0.1
  defaults:
    unpinParents: 1
    unpinChildren: 1
    unpinGroup: 0
    minMappingWeight: 0
    maxMappingWeight: 1
    minPinWeight: 0
    disableColliders: 0
    regainPinSpeed: 0.1
    collisionResistance: 1
    knockOutDistance: 0.7
    puppetMaterial: {fileID: 13400000, guid: a3fd0ffcf0d0a384ba3568687a96ab8e, type: 2}
    unpinnedMaterial: {fileID: 13400000, guid: d4563de398e2c4412a95f4317ddf4b1a, type: 2}
  groupOverrides:
  - name: Head
    groups: 02000000
    props:
      unpinParents: 1
      unpinChildren: 1
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 0.3
      collisionResistance: 1
      knockOutDistance: 0.4
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  - name: Arm, Hand, Prop
    groups: 030000000400000008000000
    props:
      unpinParents: 1
      unpinChildren: 1
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 0.3
      collisionResistance: 1
      knockOutDistance: 100
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  - name: Hips, Leg
    groups: 0000000005000000
    props:
      unpinParents: 0.9
      unpinChildren: 0.9
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 0.6
      collisionResistance: 1
      knockOutDistance: 0.4
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  - name: Foot
    groups: 06000000
    props:
      unpinParents: 0.7
      unpinChildren: 0
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 1
      collisionResistance: 1
      knockOutDistance: 0.5
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  knockOutDistance: 5
  unpinnedMuscleWeightMlp: 1
  maxRigidbodyVelocity: 1
  pinWeightThreshold: 1
  unpinnedMuscleKnockout: 1
  dropProps: 0
  canGetUp: 1
  getUpDelay: 1
  blendToAnimationTime: 0.3
  maxGetUpVelocity: 0.4
  minGetUpDuration: 2
  getUpCollisionResistanceMlp: 4
  getUpRegainPinSpeedMlp: 3
  getUpKnockOutDistanceMlp: 20
  getUpOffsetProne: {x: 0, y: 0, z: 0}
  getUpOffsetSupine: {x: 0, y: 0, z: 0}
  isQuadruped: 0
  onGetUpProne:
    switchToBehaviour: 
    animations:
    - animationState: GetUpProne
      crossfadeTime: 0.2
      layer: 0
      resetNormalizedTime: 1
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onGetUpSupine:
    switchToBehaviour: 
    animations:
    - animationState: GetUpSupine
      crossfadeTime: 0.2
      layer: 0
      resetNormalizedTime: 1
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onLoseBalance:
    switchToBehaviour: 
    animations:
    - animationState: Fall
      crossfadeTime: 0.7
      layer: 0
      resetNormalizedTime: 0
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onLoseBalanceFromPuppet:
    switchToBehaviour: 
    animations: []
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onLoseBalanceFromGetUp:
    switchToBehaviour: 
    animations: []
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onRegainBalance:
    switchToBehaviour: 
    animations: []
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  canMoveTarget: 1
--- !u!1 &4501833550144288144
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 650870691778268658}
  m_Layer: 11
  m_Name: FloorAngleBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &650870691778268658
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4501833550144288144}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.373}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8197213499133234792}
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4509747166942768668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7272893972138136223}
  m_Layer: 0
  m_Name: middle_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7272893972138136223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4509747166942768668}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013295288, y: -0.003645902, z: 0.0070136134, w: 0.9998804}
  m_LocalPosition: {x: 0.00000061035155, y: 0.044478912, z: -0.00000022888183}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5730334259293684896}
  m_Father: {fileID: 1143483047653081985}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4536221714569385165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4314299263516781743}
  - component: {fileID: 5862402671225516785}
  - component: {fileID: 4656726321353460907}
  - component: {fileID: 5162837113793957510}
  m_Layer: 7
  m_Name: PuppetMaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4314299263516781743
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4536221714569385165}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6603019320750421929}
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5862402671225516785
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4536221714569385165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  humanoidConfig: {fileID: 11400000, guid: ec93c877e6f836940b0b05babb99d713, type: 2}
  targetRoot: {fileID: 1768001276132255913}
  state: 0
  stateSettings:
    killDuration: 1
    deadMuscleWeight: 0.01
    deadMuscleDamper: 2
    maxFreezeSqrVelocity: 0.02
    freezePermanently: 0
    enableAngularLimitsOnKill: 1
    enableInternalCollisionsOnKill: 1
  mode: 0
  blendTime: 0.1
  fixTargetTransforms: 1
  solverIterationCount: 6
  visualizeTargetPose: 1
  mappingWeight: 1
  pinWeight: 1
  muscleWeight: 1
  muscleSpring: 100
  muscleDamper: 0
  pinPow: 4
  pinDistanceFalloff: 5
  angularPinning: 0
  updateJointAnchors: 1
  supportTranslationAnimation: 0
  angularLimits: 0
  internalCollisions: 0
  muscles:
  - name: pelvis
    joint: {fileID: 8264305650820093507}
    target: {fileID: 6809340320723751594}
    props:
      group: 0
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 0
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_l
    joint: {fileID: 5919505294671486721}
    target: {fileID: 1246016646434924394}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 1
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_l
    joint: {fileID: 7577523255667450435}
    target: {fileID: 5932891594977931567}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 2
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_l
    joint: {fileID: 8542744944642605514}
    target: {fileID: 2302558780381168062}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 3
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_r
    joint: {fileID: 8062682008002147427}
    target: {fileID: 3396170966632270810}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 4
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_r
    joint: {fileID: 7788491851095697022}
    target: {fileID: 784631101705099529}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 5
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_r
    joint: {fileID: 6249573664992107028}
    target: {fileID: 1771932099999606124}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 6
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: spine_02
    joint: {fileID: 2355134820906774891}
    target: {fileID: 7624248278982361176}
    props:
      group: 1
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 7
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_l
    joint: {fileID: 5556824234679884214}
    target: {fileID: 4746873980746277075}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 8
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_l
    joint: {fileID: 677984794157596908}
    target: {fileID: 3359683039249218388}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 9
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_l
    joint: {fileID: 3957457229971227730}
    target: {fileID: 5383975135103560389}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 10
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_r
    joint: {fileID: 5419504423133491858}
    target: {fileID: 1570984535673114288}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 11
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_r
    joint: {fileID: 8757836231504651189}
    target: {fileID: 800051801862851842}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 12
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_r
    joint: {fileID: 1154010614921575652}
    target: {fileID: 6703786042559524125}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 13
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: head
    joint: {fileID: 5232757845509304316}
    target: {fileID: 1975774117448653}
    props:
      group: 2
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 14
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  propMuscles: []
  solvers: []
  mapDisconnectedMuscles: 1
  storeTargetMappedState: 1
--- !u!114 &4656726321353460907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4536221714569385165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df3575b0ce53b4b3ab6c036214365cc2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  characterController: {fileID: 0}
  characterControllerLayer: 6
  ragdollLayer: 7
  ignoreCollisionWithCharacterController:
    serializedVersion: 2
    m_Bits: 4294967295
  ignoreCollisionWithRagdoll:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &5162837113793957510
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4536221714569385165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc1cfd8a0119540e8970180fc708afbd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  kinematicCollidersUpdateLimit:
    puppetsPerFrame: 15
  freeUpdateLimit:
    puppetsPerFrame: 100
  fixedUpdateLimit:
    puppetsPerFrame: 100
  collisionStayMessages: 1
  collisionExitMessages: 1
  activePuppetCollisionThresholdMlp: 0
--- !u!1 &4537993037260343503
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2312155369531029739}
  m_Layer: 0
  m_Name: thigh_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2312155369531029739
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4537993037260343503}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000012267265, y: -0.0000017183896, z: 0.00000011269007,
    w: 1}
  m_LocalPosition: {x: 0.00000012397766, y: 0.2396434, z: -0.00000011920929}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3396170966632270810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4672423183341286485
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8884557858176304778}
  - component: {fileID: 5578066629309786671}
  m_Layer: 0
  m_Name: Behaviours
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8884557858176304778
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4672423183341286485}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 13362772404359782}
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5578066629309786671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4672423183341286485}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: All Puppet Behaviours should be parented to this GameObject, the PuppetMaster
    will automatically find them from here. All Puppet Behaviours have been designed
    so that they could be simply copied from one character to another without changing
    any references. It is important because they contain a lot of parameters and
    would be otherwise tedious to set up and tweak.
--- !u!1 &4672430614251258486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3859776997442730159}
  m_Layer: 0
  m_Name: CameraAutoLookAt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3859776997442730159
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4672430614251258486}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.95216703, z: 4.6503477}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5066961399519885503}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4782064217587161119
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4872260779970431100}
  - component: {fileID: 6607182855594882500}
  - component: {fileID: 3387135692686593795}
  m_Layer: 0
  m_Name: MovementModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4872260779970431100
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4782064217587161119}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4730494400458778153}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6607182855594882500
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4782064217587161119}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96268f2884c13d24eb4c6351a3388a7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  movementConfig: {fileID: 11400000, guid: ecac2dc6864880d4fb362f2dade9c893, type: 2}
  <ControllerIndex>k__BackingField: 0
  m_useAnimancer: 1
  <MainState>k__BackingField: 000000000100000002000000
  _moduleSubState: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  animationModule: {fileID: 3387135692686593795}
  characterParameters: {fileID: 3336923530583100265}
  IsMoving: 0
  _subState: 0
  InputAngleDump: 0
  inputAngle: 0
  angleDifference: 0
  m_playerTransform: {fileID: 1768001276132255913}
  m_changeDirection: 0
  m_waitForChangeDirection: 0
  m_waitForStop: 0.5
  m_isTurningBack: 0
  _isTransitioning: 0
--- !u!114 &3387135692686593795
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4782064217587161119}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c977cd4b40934cb887c285ed1f8476e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 8830726950650311852}
    - {fileID: 9195335255939506981}
    - {fileID: 5954690637389537017}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <States>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.Enum, mscorlib],[Module.Mono.Animancer.RealsticFemale.CharacterState,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.ObjectEqualityComparer`1[[System.Enum, mscorlib]],
        mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 2|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 3|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 4|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 5|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 3
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  m_useAnimancer: 0
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 000000000100000002000000
  <SubState>k__BackingField: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  _Animancer: {fileID: 712441798001151660}
  _Parameters: {fileID: 3336923530583100265}
--- !u!1 &4926106861042470472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6650104368019110041}
  m_Layer: 0
  m_Name: VaultCharRot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6650104368019110041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4926106861042470472}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2463105333083752492}
  m_Father: {fileID: 2210427295702728430}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4929609704327695710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6938360488027384646}
  m_Layer: 21
  m_Name: hammer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6938360488027384646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4929609704327695710}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.035133835, z: -0.07476188}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4956609387739663328
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2152997211138788668}
  m_Layer: 0
  m_Name: pinky_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2152997211138788668
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4956609387739663328}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0072141318, y: -0.000017000275, z: 0.04986973, w: 0.9987297}
  m_LocalPosition: {x: 0.00000030517577, y: 0.01617073, z: -0.00000029563904}
  m_LocalScale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4127082261127725367}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5022023448687215414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6631653080207553101}
  m_Layer: 6
  m_Name: HoldPos_AppleNoBone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6631653080207553101
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5022023448687215414}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49813393, y: -0.41198018, z: -0.45254937, w: 0.6142752}
  m_LocalPosition: {x: -0.226, y: 1.71, z: 0.299}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: -193.83301, y: 99.74, z: 95.562996}
--- !u!1 &5030238585871163844
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6862282147661311009}
  - component: {fileID: 7916506578888296180}
  m_Layer: 0
  m_Name: InputModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6862282147661311009
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5030238585871163844}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4730494400458778153}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7916506578888296180
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5030238585871163844}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaf09bfab896407fa91b5c272c57cb0d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 3336923530583100265}
    - {fileID: 5562475231938505990}
    - {fileID: 7919645375193850435}
    - {fileID: 7029741597687228658}
    - {fileID: 792671241793180101}
    - {fileID: 2742023919547625688}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <OutputValue>k__BackingField
      Entry: 6
      Data: 
    - Name: _needInputComponents
      Entry: 7
      Data: 0|System.Collections.Generic.List`1[[DefaultNamespace.Mono.Interface.INeedInput`1[[Module.Mono.Animancer.RealsticFemale.CharacterParameter,
        General]], Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 12
      Data: 6
    - Name: 
      Entry: 10
      Data: 0
    - Name: 
      Entry: 10
      Data: 1
    - Name: 
      Entry: 10
      Data: 2
    - Name: 
      Entry: 10
      Data: 3
    - Name: 
      Entry: 10
      Data: 4
    - Name: 
      Entry: 10
      Data: 5
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  m_playerTransform: {fileID: 1768001276132255913}
  inputAngleMultiplier: 0
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 000000000100000002000000
  <SubState>k__BackingField: 0
  CharacterPositionCompass: {fileID: 5949918129644153813}
  InputDirectionCompass: {fileID: 6132481587407583268}
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  <State>k__BackingField: 0
  rawHorizontalInput: 0
  rawVerticalInput: 0
  <horizontalInput>k__BackingField: 0
  <verticalInput>k__BackingField: 0
  <startInputAngle>k__BackingField: 0
  <inputAngle>k__BackingField: 0
  <stopInputAngle>k__BackingField: 0
  <inputMagnitude>k__BackingField: 0
  <runFactor>k__BackingField: 0
  <playerInputDirection>k__BackingField: {x: 0, y: 0, z: 0}
  m_isRunning: 0
  m_isLeftMoving: 0
  m_isRightMoving: 0
  m_isFwrdMoving: 0
  m_isBackMoving: 0
  m_isLeftFootstep: 0
  m_isRightFootstep: 0
  m_currentWeaponIndex: 0
  m_maxWeaponIndex: 1
  m_equipWeapon: 0
  m_unEquipWeapon: 0
  m_interact: 0
  EnableAutoAim: 1
  IsAiming: 0
  IsDetectTarget: 0
  EnableAutoShooting: 1
  IsShooting: 0
  LastShootingState: 0
--- !u!1 &5064687355779637033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1256160826407134159}
  m_Layer: 0
  m_Name: CC_Base_Teeth01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1256160826407134159
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5064687355779637033}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00023426232, z: 0.00003416539, w: 0.000011044115}
  m_LocalPosition: {x: -0.0010065723, y: 0.0037287902, z: -0.000025710386}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8895518437609776394}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5114865183753401722
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4207601964448435926}
  m_Layer: 0
  m_Name: CC_Base_JawRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4207601964448435926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5114865183753401722}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000003424143, y: -0.00006812931, z: 1, w: -0.000017310314}
  m_LocalPosition: {x: 0.026865939, y: 0.011197052, z: -0.00015041605}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7047133969429612563}
  - {fileID: 6332157594019442039}
  m_Father: {fileID: 7040960904282605691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5117818072247082232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8463786004102510515}
  - component: {fileID: 7142898169479841738}
  m_Layer: 0
  m_Name: MP4AimingSubModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8463786004102510515
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5117818072247082232}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9078335296355087865}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7142898169479841738
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5117818072247082232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 583b0b8f3bcc4bf6b0e1be30c2aa1e2c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AimingData:
    HasLeftHandIK: 1
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
--- !u!1 &5156483948296169881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7309246504749364867}
  m_Layer: 0
  m_Name: CC_Base_L_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7309246504749364867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5156483948296169881}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999708, y: 0.5000029, z: 0.49999708, w: 0.5000029}
  m_LocalPosition: {x: 0.06261502, y: 0.05849472, z: 0.03165966}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7040960904282605691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5168993835980424612
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2555468018400716107}
  m_Layer: 0
  m_Name: GroundRaycaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2555468018400716107
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5168993835980424612}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4421362856041145290}
  - {fileID: 3737002327134446675}
  - {fileID: 7722277206722421182}
  - {fileID: 8261531813573869650}
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5270124260168387165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2912570544674203490}
  m_Layer: 0
  m_Name: clavicle_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2912570544674203490
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5270124260168387165}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009416457, y: 0.16666275, z: -0.6722112, w: 0.7212953}
  m_LocalPosition: {x: 0.047479425, y: 0.21393646, z: 0.0025744247}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1570984535673114288}
  m_Father: {fileID: 6354417209521280469}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5278360336054111132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6135183471443248035}
  - component: {fileID: 6535504906633297783}
  - component: {fileID: 8250613970021064793}
  m_Layer: 0
  m_Name: QuadTextured
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6135183471443248035
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5278360336054111132}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7099969, y: 0, z: 0, w: 0.70420486}
  m_LocalPosition: {x: 0, y: 0.12, z: -0.02156341}
  m_LocalScale: {x: 0.5, y: 0.50000006, z: 0.50000006}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7041930962628465281}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6535504906633297783
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5278360336054111132}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8250613970021064793
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5278360336054111132}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5319643890459908578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2682979858017989496}
  m_Layer: 0
  m_Name: spine_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2682979858017989496
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5319643890459908578}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268, w: 0.9867998}
  m_LocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7624248278982361176}
  m_Father: {fileID: 6809340320723751594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5320479383730271571
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1354757304456323966}
  - component: {fileID: 5245768833647091730}
  - component: {fileID: 390228523632851654}
  - component: {fileID: 8757836231504651189}
  m_Layer: 7
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1354757304456323966
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5320479383730271571}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016186383, y: 0.000018548217, z: 0.007330766, w: 0.9999731}
  m_LocalPosition: {x: 0.00029290843, y: 0.2814219, z: 0.00000090067647}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 151253809231129524}
  m_Father: {fileID: 2528320946427295909}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5245768833647091730
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5320479383730271571}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &390228523632851654
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5320479383730271571}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07753206
  m_Height: 0.27950746
  m_Direction: 1
  m_Center: {x: -0.000000463915, y: 0.12450803, z: 0.0000000035354044}
--- !u!153 &8757836231504651189
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5320479383730271571}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5789275642811653956}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.8935054
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.10649
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &5333932442924628984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5160767894998586803}
  m_Layer: 0
  m_Name: thigh_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5160767894998586803
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5333932442924628984}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000008643836, y: 0.0000018727438, z: -6.0897634e-21, w: 1}
  m_LocalPosition: {x: -0.0000002813339, y: 0.23965019, z: -0.00000030994414}
  m_LocalScale: {x: 1.0000002, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1246016646434924394}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5337084010418489628
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1570984535673114288}
  m_Layer: 0
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1570984535673114288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5337084010418489628}
  serializedVersion: 2
  m_LocalRotation: {x: 0.117331214, y: -0.0024746358, z: -0.027261361, w: 0.99271554}
  m_LocalPosition: {x: -0.00000015258789, y: 0.100599535, z: -0.0000004816055}
  m_LocalScale: {x: 0.99999976, y: 0.99999964, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 800051801862851842}
  - {fileID: 868485759455274028}
  m_Father: {fileID: 2912570544674203490}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5338293214035926815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1316463068385753957}
  m_Layer: 0
  m_Name: middle_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1316463068385753957
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5338293214035926815}
  serializedVersion: 2
  m_LocalRotation: {x: -0.074639395, y: -0.0028822029, z: -0.0406939, w: 0.9963758}
  m_LocalPosition: {x: -0.00000030517577, y: 0.0929306, z: 0.000000019073486}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3975190985635198000}
  m_Father: {fileID: 6703786042559524125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5369111705470293734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7556605754010412423}
  m_Layer: 0
  m_Name: thumb_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7556605754010412423
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5369111705470293734}
  serializedVersion: 2
  m_LocalRotation: {x: -0.16171406, y: -0.004255523, z: 0.092745736, w: 0.98246056}
  m_LocalPosition: {x: -0.0000007629394, y: 0.05436756, z: -0.00000072479247}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6986818677904699797}
  m_Father: {fileID: 318315931718969326}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5383989349721342015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6411998766675819667}
  - component: {fileID: 511570501894979341}
  - component: {fileID: 4327803439486566842}
  - component: {fileID: 2355134820906774891}
  m_Layer: 7
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6411998766675819667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5383989349721342015}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037494026, y: 0.00000367303, z: 0.00011810292, w: 0.9992969}
  m_LocalPosition: {x: 0.000027147595, y: 0.11331209, z: 0.030964524}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3587060510133478845}
  - {fileID: 2528320946427295909}
  - {fileID: 3146388501999888161}
  m_Father: {fileID: 6603019320750421929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &511570501894979341
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5383989349721342015}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &4327803439486566842
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5383989349721342015}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.29746693, y: 0.42069203, z: 0.17848016}
  m_Center: {x: 0.00012554851, y: 0.1952784, z: -0.039148986}
--- !u!153 &2355134820906774891
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5383989349721342015}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8408529025615113783}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &5394438766037723012
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8872734216084058634}
  - component: {fileID: 3278093042623125549}
  m_Layer: 0
  m_Name: Middle_Ponytail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8872734216084058634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5394438766037723012}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &3278093042623125549
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5394438766037723012}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2d91b221636cd93469c2cff02bb387f9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1155122597573229543, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 7624248278982361176}
  - {fileID: 6354417209521280469}
  - {fileID: 4254692493541640014}
  - {fileID: 1975774117448653}
  - {fileID: 1604281555635059624}
  - {fileID: 3316849888583807694}
  - {fileID: 7620355634370948199}
  - {fileID: 2912570544674203490}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 7624248278982361176}
  m_AABB:
    m_Center: {x: -0.0010192692, y: 0.46571648, z: -0.122491054}
    m_Extent: {x: 0.091920495, y: 0.17998058, z: 0.1596455}
  m_DirtyAABB: 0
--- !u!1 &5437567815235561999
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6332157594019442039}
  m_Layer: 0
  m_Name: CC_Base_Tongue01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6332157594019442039
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5437567815235561999}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029571867, y: 0.006775168, z: -0.11056743, w: 0.9938455}
  m_LocalPosition: {x: -0.02315749, y: 0.011044769, z: -0.00015940386}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2102168643567736953}
  m_Father: {fileID: 4207601964448435926}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5456612021268780677
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7491053147720463444}
  m_Layer: 21
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7491053147720463444
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5456612021268780677}
  serializedVersion: 2
  m_LocalRotation: {x: 0.79035556, y: -0.33337912, z: 0.37890536, w: -0.34731433}
  m_LocalPosition: {x: 0.005, y: -0.053, z: -0.039}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &5512247769850042473
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7722277206722421182}
  m_Layer: 0
  m_Name: GroundRaycaster4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7722277206722421182
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5512247769850042473}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.0000001860467, z: 0, w: 1}
  m_LocalPosition: {x: -0.16829455, y: 0, z: 0.00000015692554}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2555468018400716107}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5565326302883590949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2199128037404243051}
  - component: {fileID: 4653231386930053350}
  - component: {fileID: 6623912556636528090}
  m_Layer: 21
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2199128037404243051
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5565326302883590949}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2810497911127419708}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!33 &4653231386930053350
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5565326302883590949}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6623912556636528090
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5565326302883590949}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5572047069825920409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4778733340781212292}
  m_Layer: 0
  m_Name: CC_Base_Tongue03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4778733340781212292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5572047069825920409}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000046749043, y: -0.0010550104, z: 0.11461715, w: 0.9934092}
  m_LocalPosition: {x: -0.013736267, y: 0.000017242432, z: 0.00000092945993}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2102168643567736953}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5590180362724236047
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4841403013678523602}
  - component: {fileID: 3733632751101440226}
  - component: {fileID: 6353073714078396353}
  m_Layer: 21
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4841403013678523602
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5590180362724236047}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000007, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2810497911127419708}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!33 &3733632751101440226
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5590180362724236047}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6353073714078396353
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5590180362724236047}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5590319280302386608
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7037241707615803511}
  - component: {fileID: 4508602400886010609}
  - component: {fileID: 5853090283237602218}
  m_Layer: 6
  m_Name: InteractorPoints
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7037241707615803511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5590319280302386608}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4508602400886010609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5590319280302386608}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b679288a78ae234490bd305e094444d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  inventory: {fileID: 5350220601721065077}
  holdPoints:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 6631653080207553101}
  - {fileID: 0}
  onBodyPoints:
  - bodyPoint: {fileID: 0}
    bodyPointEnd: {fileID: 0}
    endSpeedMult: 1
    currentObject: {fileID: 0}
  - bodyPoint: {fileID: 0}
    bodyPointEnd: {fileID: 0}
    endSpeedMult: 1
    currentObject: {fileID: 0}
  - bodyPoint: {fileID: 0}
    bodyPointEnd: {fileID: 0}
    endSpeedMult: 1
    currentObject: {fileID: 0}
  throwUpPoint: {fileID: 0}
  throwDownPoint: {fileID: 0}
  lineRenderer: {fileID: 5853090283237602218}
  lineLenght: 1
--- !u!120 &5853090283237602218
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5590319280302386608}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 61e97c49a7059c54eb5ed8ff140cd4ad, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: 0, y: 0, z: 0}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  - {x: 0, y: 0, z: 1}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.02
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0.57254905}
      key3: {r: 0, g: 0, b: 0, a: 0.5019608}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 12143
      atime3: 21627
      atime4: 65535
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 1
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 0
--- !u!1 &5615533298249553943
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4031297349607152135}
  - component: {fileID: 1553757846737663554}
  - component: {fileID: 3336923530583100265}
  - component: {fileID: 7421254585528994050}
  - component: {fileID: 701300999452479702}
  - component: {fileID: 5596790557875754930}
  - component: {fileID: 774308589952597959}
  - component: {fileID: 2194881769724018296}
  - component: {fileID: 2202364198049297328}
  m_Layer: 0
  m_Name: Female1New
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4031297349607152135
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 0.9, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9086886816831505705}
  - {fileID: 8884557858176304778}
  - {fileID: 7329253386303068935}
  - {fileID: 6975277655420141540}
  - {fileID: 7530810731390684953}
  - {fileID: 4314299263516781743}
  - {fileID: 1768001276132255913}
  - {fileID: 779325286296278069}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1553757846737663554
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a14bfc2338bf4611b10d398151e4dca8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 7916506578888296180}
    - {fileID: 7919645375193850435}
    - {fileID: 5562475231938505990}
    - {fileID: 792671241793180101}
    - {fileID: 2742023919547625688}
    - {fileID: 6607182855594882500}
    - {fileID: 3387135692686593795}
    - {fileID: 7029741597687228658}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: _modules
      Entry: 7
      Data: 0|System.Collections.Generic.List`1[[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 12
      Data: 8
    - Name: 
      Entry: 10
      Data: 0
    - Name: 
      Entry: 10
      Data: 1
    - Name: 
      Entry: 10
      Data: 2
    - Name: 
      Entry: 10
      Data: 3
    - Name: 
      Entry: 10
      Data: 4
    - Name: 
      Entry: 10
      Data: 5
    - Name: 
      Entry: 10
      Data: 6
    - Name: 
      Entry: 10
      Data: 7
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: nextUpdateTimes
      Entry: 7
      Data: 1|System.Collections.Generic.Dictionary`2[[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp],[System.Single, mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 2|System.Collections.Generic.ObjectEqualityComparer`1[[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _needComponents
      Entry: 7
      Data: 3|System.Collections.Generic.List`1[[DefaultNamespace.Mono.Interface.INeedComponent,
        Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  <ControllerIndex>k__BackingField: 0
  IsInitialized: 0
  StateManager: {fileID: 7421254585528994050}
  RagdollParent: {fileID: 4314299263516781743}
  CharacterParent: {fileID: 1768001276132255913}
  Colliders:
  - {fileID: 6603019320750421929}
  - {fileID: 2939897042593437468}
  - {fileID: 6393905998216405920}
  - {fileID: 7443338930424432770}
  - {fileID: 7892479749728525563}
  - {fileID: 6411998766675819667}
  - {fileID: 3587060510133478845}
  - {fileID: 4214021402500490547}
  - {fileID: 2528320946427295909}
  - {fileID: 1354757304456323966}
--- !u!114 &3336923530583100265
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1895a8d88d74273ba2e4fb0324e66e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <CurrentState>k__BackingField
      Entry: 6
      Data: 
  m_animator: {fileID: 7342432587237116706}
  Horizontal:
    EnableLearping: 0
    Timer: 0.05
    Value: 0
  Vertical:
    EnableLearping: 0
    Timer: 0.05
    Value: 0
  IsAiming: 0
  LastAimingStatus: 0
  IsChangeDirection: 0
  IsAutoAiming: 0
  IsDetectTarget: 0
  IsAutoShooting: 0
  IsShooting: 0
  <IsTargetBehind>k__BackingField: 0
  <WantsToRotateBehindTarget>k__BackingField: 0
  RunningFactor:
    EnableLearping: 0
    Timer: 0
    Value: 0
  IsStopping: 0
  InputMagnitude:
    EnableLearping: 0
    Timer: 0
    Value: 0
  StartWalkAngle:
    EnableLearping: 0
    Timer: 0
    Value: 0
  InputAngle:
    EnableLearping: 0
    Timer: 0.25
    Value: 0
  StopWalkAngle:
    EnableLearping: 0
    Timer: 0
    Value: 0
  CurrentPlayerSpeed:
    EnableLearping: 0
    Timer: 0
    Value: 0
  PlayerInputDirection:
    EnableLearping: 0
    Timer: 0.1
    Value: {x: 0, y: 0, z: 0}
  IsGrounded: 1
  IsLeftMoving: 0
  IsRightMoving: 0
  IsFwrdMoving: 0
  IsBackMoving: 0
  IsLeftFootstep: 0
  IsRightFootstep: 0
  WeaponIndex: 0
  AimTarget:
    x: 0
    y: 0
    z: 0
  PlayerTransform: {fileID: 0}
  <InputName>k__BackingField: 000000000100000002000000030000000400000005000000060000000700000008000000090000000a0000000b0000000c0000000d0000000e0000000f000000100000001100000012000000130000001400000015000000160000001700000018000000
--- !u!114 &7421254585528994050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b9a63380f5048a8a3159c7b9a9d9834, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: subStates
      Entry: 6
      Data: 
    - Name: subModules
      Entry: 6
      Data: 
    - Name: statesHolder
      Entry: 6
      Data: 
  currentMainState: 0
--- !u!114 &701300999452479702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f56769f332de420e9a8d8b1ecc602b0f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _puppetMaster: {fileID: 5862402671225516785}
  layers:
    serializedVersion: 2
    m_Bits: 0
  unpin: 100
  force: 20
--- !u!114 &5596790557875754930
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01f39886f6899de409e6a9c737a8a236, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  showDetectionRadius: 0
  detectionRadiusColor: {r: 0.2, g: 0.8, b: 0.2, a: 1}
  detectionAngleColor: {r: 0.8, g: 0.8, b: 0.2, a: 1}
  configurationRadiusColor: {r: 1, g: 0.5, b: 0, a: 1}
  detectedEnemiesCount: 0
  enemiesWithHealthBarCount: 0
--- !u!114 &774308589952597959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7edcfcd63b1447acb63699eeb3966c67, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enableStateSynchronization: 1
  enableDebugLogging: 1
  synchronizationInterval: 0.016
  currentUnifiedState:
    mainState: 0
    movementState: 0
    weaponState: 0
    animationState: 0
    weaponModuleState: 0
    timestamp: 0
    frameCount: 0
    isValid: 0
  previousUnifiedState:
    mainState: 0
    movementState: 0
    weaponState: 0
    animationState: 0
    weaponModuleState: 0
    timestamp: 0
    frameCount: 0
    isValid: 0
  currentMovementState: 0
  currentWeaponState: 2
  currentAnimationState: 0
  currentWeaponModule: -1
  currentMainState: 0
  hasMovementChanged: 0
  hasWeaponChanged: 0
  hasAnimationChanged: 0
  hasMainStateChanged: 0
  totalStateChanges: 0
  synchronizationUpdates: 0
  lastSynchronizationTime: 0
  targetLossCooldown: 0.5
  isInTargetLossCooldown: 0
--- !u!114 &2194881769724018296
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f3b19422a1c92d46a8d4e3428abb3c9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  unifiedAnimationController: {fileID: 3010373431568239746}
  currentUpperBodyAnimation:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
--- !u!114 &2202364198049297328
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5615533298249553943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cb90f738e9c0a194399f6bca51c69be7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enableDebugGUI: 1
  enableRealTimeLogging: 0
  showDetailedInfo: 1
  debugToggleKey: 282
  unifiedController: {fileID: 0}
  eventManager: {fileID: 0}
  transitionManager: {fileID: 0}
  layeredAdapter: {fileID: 0}
--- !u!1 &5764471697958204226
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9184654208057938550}
  m_Layer: 0
  m_Name: ball_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9184654208057938550
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5764471697958204226}
  serializedVersion: 2
  m_LocalRotation: {x: 0.47116238, y: -0.020159548, z: 0.024802627, w: 0.8814672}
  m_LocalPosition: {x: -0.0000018787383, y: 0.14396666, z: 0.0000007390976}
  m_LocalScale: {x: 1, y: 0.9999998, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2302558780381168062}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5936669122160762626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5730334259293684896}
  m_Layer: 0
  m_Name: middle_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5730334259293684896
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5936669122160762626}
  serializedVersion: 2
  m_LocalRotation: {x: -0.007730959, y: 0.00016430892, z: 0.0217835, w: 0.99973285}
  m_LocalPosition: {x: 0.000037231446, y: 0.02905731, z: 0.000018892288}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7272893972138136223}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5949918129644153813
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6975277655420141540}
  - component: {fileID: 7891277441569663843}
  m_Layer: 0
  m_Name: CharacterPositionCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6975277655420141540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949918129644153813}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7041930962628465281}
  - {fileID: 653180301364527254}
  - {fileID: 8953174486455695084}
  - {fileID: 2555468018400716107}
  - {fileID: 8472509196334568073}
  - {fileID: 2673969226716299816}
  - {fileID: 3714135272472090369}
  - {fileID: 2210427295702728430}
  - {fileID: 7704864047249165022}
  - {fileID: 4839714920747588693}
  - {fileID: 650870691778268658}
  - {fileID: 2995145027097432860}
  - {fileID: 7795360010642506923}
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7891277441569663843
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949918129644153813}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa8b96fd653afbe428a148130cdf67b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  parent: {fileID: 6342451854554718223}
  worldPositionStays: 1
--- !u!1 &6088614054021276525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1771932099999606124}
  m_Layer: 0
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1771932099999606124
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6088614054021276525}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213843, y: -0.07327937, z: 0.035943985, w: 0.9615725}
  m_LocalPosition: {x: -0.00000042438506, y: 0.4937627, z: -0.0000005340576}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7113810491443191821}
  - {fileID: 9017272927277311250}
  m_Father: {fileID: 784631101705099529}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6132481587407583268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7329253386303068935}
  - component: {fileID: 7919645375193850435}
  m_Layer: 0
  m_Name: InputDirectionCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7329253386303068935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6132481587407583268}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5066961399519885503}
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7919645375193850435
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6132481587407583268}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1db305b54b5c3934dbcbddbda5d14f5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  <ControllerIndex>k__BackingField: 0
  characterParameter: {fileID: 3336923530583100265}
  reachedTarget: 0
  CharacterPositionCompass: {fileID: 5949918129644153813}
  minMagnitude: 0.01
  lastRotation: {x: 0, y: 0, z: 0, w: 0}
  desiredRotation: {x: 0, y: 0, z: 0, w: 0}
  speed: 25
  <MainState>k__BackingField: 000000000100000002000000
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  <SubState>k__BackingField: 0
  <InputName>k__BackingField: 02000000
  m_inputVector: {x: 0, y: 0, z: 0}
--- !u!1 &6182406225297868995
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4046584383438219079}
  m_Layer: 21
  m_Name: ejection
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4046584383438219079
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6182406225297868995}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.00069135264, z: 0.058437884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3925261838396088709}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6268088833059061834
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6986818677904699797}
  m_Layer: 0
  m_Name: thumb_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6986818677904699797
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6268088833059061834}
  serializedVersion: 2
  m_LocalRotation: {x: -0.045648206, y: 0.0017702165, z: 0.029804673, w: 0.9985113}
  m_LocalPosition: {x: -0.00000015258789, y: 0.026587524, z: 0.00000024318695}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7556605754010412423}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6342451854554718223
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1768001276132255913}
  - component: {fileID: 7342432587237116706}
  - component: {fileID: 1460080192091817139}
  - component: {fileID: 2335054676565968046}
  - component: {fileID: 4819793649072361018}
  - component: {fileID: 712441798001151660}
  - component: {fileID: 4324291176780966862}
  - component: {fileID: 1783658145224123576}
  - component: {fileID: 3010373431568239746}
  - component: {fileID: 3109036664250781863}
  - component: {fileID: 3528834485946640581}
  m_Layer: 6
  m_Name: Female1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1768001276132255913
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1.0638298, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4730494400458778153}
  - {fileID: 117205096757043403}
  - {fileID: 6249227604688449147}
  - {fileID: 6449606165044503461}
  - {fileID: 753572775222062678}
  - {fileID: 7126979179419134157}
  - {fileID: 8392451243752310651}
  - {fileID: 5126402618727581682}
  - {fileID: 8872734216084058634}
  - {fileID: 37560226843851422}
  - {fileID: 2252395986020352724}
  - {fileID: 1373038588085074837}
  - {fileID: 7037241707615803511}
  - {fileID: 1056637287934946271}
  - {fileID: 3195304526723373558}
  - {fileID: 3338510557000658300}
  - {fileID: 6631653080207553101}
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &7342432587237116706
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Controller: {fileID: 9100000, guid: 063544d9180ba394b9e7c497c8372a7d, type: 2}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!135 &1460080192091817139
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1.3668647
  m_Center: {x: 0, y: 0.84438926, z: 0}
--- !u!114 &2335054676565968046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9832ef7d144a5a4ca35019d9c2edd7a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  crosshairEnable: 1
--- !u!114 &4819793649072361018
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc915763b98747f5abd5c196122525e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _audioSource: {fileID: 0}
  _FootstepEvents: {fileID: 0}
  _FootSources: []
  _baseAnimationSpeed: 1
  _currentSpeedMultiplier: 1
  OnLeftFootstepHit:
    m_PersistentCalls:
      m_Calls: []
  OnRightFootstepHit:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &712441798001151660
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6ad7b53f86f9da4da426b673c422513, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animator: {fileID: 7342432587237116706}
  _ActionOnDisable: 0
  _PlayAutomatically: 0
  _Animations: []
  _Controller:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Controller: {fileID: 0}
    _ActionsOnStop: 
--- !u!114 &4324291176780966862
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5c3ee4d6bc22a7499986d03b341d4bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playerTransform: {fileID: 1768001276132255913}
  rotationSpeed: 720
--- !u!114 &1783658145224123576
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da8c4087d05341cdbcc36c359902eb7f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animationController: {fileID: 3010373431568239746}
  configManager: {fileID: 3528834485946640581}
  enableTransitionValidation: 1
  enableTransitionQueue: 1
  enableInterruptibleTransitions: 1
  transitionTimeoutDuration: 5
  enableDebugLogging: 0
  showTransitionGizmos: 0
  trackTransitionHistory: 0
--- !u!114 &3010373431568239746
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56d8adb6ad0341b1954cd991f77485d1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  useAnimancer: 1
  enableDebugLogging: 0
  defaultTransitionDuration: 0.25
  upperBodyMask: {fileID: 31900000, guid: 2ee86d3a5e2d3cf4fa965c906da1eb50, type: 2}
  upperBodyFadeDuration: 0.25
  enableLayeredAnimations: 1
  characterAnimationModule: {fileID: 3387135692686593795}
  weaponAnimationModule: {fileID: 2742023919547625688}
  layeredAnimationManager: {fileID: 2194881769724018296}
  stateManager: {fileID: 7421254585528994050}
  characterParameters: {fileID: 3336923530583100265}
  useEventDrivenManager: 0
  eventDrivenManager: {fileID: 0}
  useAdvancedTransitions: 0
  transitionManager: {fileID: 0}
--- !u!114 &3109036664250781863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0275931b9e2744e5821f5c46a26e03bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  aimingConfig: {fileID: 11400000, guid: a05d9d69b4b2427685614e4622e177c7, type: 2}
  baseMovementSpeed: 5
  sprintSpeedMultiplier: 1.5
  aimingSpeedCurve:
    serializedVersion: 2
    m_Curve: []
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  strafingSpeedCurve:
    serializedVersion: 2
    m_Curve: []
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  backpedalSpeedCurve:
    serializedVersion: 2
    m_Curve: []
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  targetDistanceSpeedCurve:
    serializedVersion: 2
    m_Curve: []
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  minTargetDistance: 0
  maxTargetDistance: 0
  enableDistanceBasedSpeed: 1
  forwardThreshold: 0
  backwardThreshold: 0
  strafeThreshold: 0
  speedTransitionTime: 0.2
  speedTransitionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  enableDebugLogging: 0
  enablePerformanceOptimization: 1
  updateInterval: 0.016
--- !u!114 &3528834485946640581
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6342451854554718223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8d7aa1a40f76438094b6f835ecb2c53e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  systemConfiguration: {fileID: 11400000, guid: f963cfc168a5e574489f9767ce98f396,
    type: 2}
  additionalConfigurations: []
  autoLoadConfigurations: 1
  enableConfigurationCaching: 1
  validateConfigurationsOnLoad: 1
--- !u!1 &6357757741361336831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3396170966632270810}
  m_Layer: 0
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3396170966632270810
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6357757741361336831}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00026941087, y: 0.00016035014, z: 0.9997506, w: 0.02232932}
  m_LocalPosition: {x: 0.0974114, y: -0.021429215, z: -0.0022249054}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 784631101705099529}
  - {fileID: 2312155369531029739}
  m_Father: {fileID: 6809340320723751594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6421137089373801456
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7839827620740900589}
  m_Layer: 0
  m_Name: thumb_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7839827620740900589
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6421137089373801456}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30981874, y: 0.12231633, z: 0.09624521, w: 0.9379701}
  m_LocalPosition: {x: -0.008979035, y: 0.022824248, z: 0.018420162}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1390068458671367855}
  m_Father: {fileID: 5383975135103560389}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6479873636043813786
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4359108457390472948}
  - component: {fileID: 6116117021759453859}
  m_Layer: 21
  m_Name: hammer 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4359108457390472948
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6479873636043813786}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 893050845294910504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6116117021759453859
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6479873636043813786}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 5970359239379961709, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 1383474775041360180}
  - {fileID: 3059993682524517977}
  - {fileID: 3925261838396088709}
  - {fileID: 4046584383438219079}
  - {fileID: 5284782164835845971}
  - {fileID: 6938360488027384646}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3925261838396088709}
  m_AABB:
    m_Center: {x: 0, y: -0.0062306616, z: -0.03359142}
    m_Extent: {x: 0.0032007988, y: 0.012480823, z: 0.008506931}
  m_DirtyAABB: 0
--- !u!1 &6505947932962439217
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6288012920567654403}
  - component: {fileID: 969609528223833965}
  m_Layer: 0
  m_Name: PistolAimingSubModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6288012920567654403
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6505947932962439217}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9078335296355087865}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &969609528223833965
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6505947932962439217}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ad99c1b3efc4acb90269d1b9eb71c1e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AimingData:
    HasLeftHandIK: 1
    LeftHandFixTransform: {fileID: 4357931742740439138}
    AimTarget: {fileID: 2820369101759257821}
    Pointer: {fileID: 8541219829470538690}
--- !u!1 &6582866392046064558
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 868485759455274028}
  m_Layer: 0
  m_Name: upperarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &868485759455274028
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6582866392046064558}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000019607223, y: -0.00000022029232, z: 0.0000012521631,
    w: 1}
  m_LocalPosition: {x: 0.00000045776366, y: 0.14070854, z: 0.0000006866455}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1570984535673114288}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6625879291024229249
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3059993682524517977}
  m_Layer: 21
  m_Name: mag
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3059993682524517977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6625879291024229249}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.019175243, z: -0.047334816}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6699476597385701339
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6783681568393303306}
  m_Layer: 0
  m_Name: pinky_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6783681568393303306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6699476597385701339}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0070990967, y: -0.0012974498, z: -0.000019510031, w: 0.99997395}
  m_LocalPosition: {x: 0.00000015258789, y: 0.033094022, z: -0.000000085830685}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8061476685868375142}
  m_Father: {fileID: 6722320523965547818}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6727373370589146040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1246016646434924394}
  m_Layer: 0
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1246016646434924394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6727373370589146040}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0002580825, y: 0.00028840144, z: 0.9997715, w: -0.021374442}
  m_LocalPosition: {x: -0.09741045, y: -0.021480026, z: -0.0022066522}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5932891594977931567}
  - {fileID: 5160767894998586803}
  m_Father: {fileID: 6809340320723751594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6753509517294895486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9017272927277311250}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9017272927277311250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6753509517294895486}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86623275, y: 0.017648226, z: -0.010600417, w: 0.49921635}
  m_LocalPosition: {x: 0.0028650907, y: 0.22237192, z: 0.113568485}
  m_LocalScale: {x: 1, y: 1.0638298, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1771932099999606124}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6852714991633896669
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6354417209521280469}
  m_Layer: 0
  m_Name: spine_03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6354417209521280469
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6852714991633896669}
  serializedVersion: 2
  m_LocalRotation: {x: -0.15933207, y: -0.00021606042, z: -0.0018017701, w: 0.9872234}
  m_LocalPosition: {x: -7.8231094e-10, y: 0.12466316, z: 0.0000017356872}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3316849888583807694}
  - {fileID: 7620355634370948199}
  - {fileID: 1604281555635059624}
  - {fileID: 2912570544674203490}
  - {fileID: 4254692493541640014}
  m_Father: {fileID: 7624248278982361176}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6859730681878559718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 800051801862851842}
  m_Layer: 0
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &800051801862851842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6859730681878559718}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016458846, y: 0.000018592604, z: 0.0073306826, w: 0.9999731}
  m_LocalPosition: {x: 0.0000007629394, y: 0.28141707, z: 0.0000009012222}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6703786042559524125}
  - {fileID: 7816194994385151601}
  m_Father: {fileID: 1570984535673114288}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6914733879893882010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2797939799877729073}
  - component: {fileID: 7905042474349537591}
  m_Layer: 21
  m_Name: slide 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2797939799877729073
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6914733879893882010}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 893050845294910504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7905042474349537591
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6914733879893882010}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8380061810295591180, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 1383474775041360180}
  - {fileID: 3059993682524517977}
  - {fileID: 3925261838396088709}
  - {fileID: 4046584383438219079}
  - {fileID: 5284782164835845971}
  - {fileID: 6938360488027384646}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3925261838396088709}
  m_AABB:
    m_Center: {x: 0, y: -0.009797938, z: 0.06299989}
    m_Extent: {x: 0.01610092, y: 0.023823507, z: 0.10387771}
  m_DirtyAABB: 0
--- !u!1 &6992466330177432017
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8895518437609776394}
  m_Layer: 0
  m_Name: CC_Base_UpperJaw
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8895518437609776394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6992466330177432017}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00005004184, y: 0.0000016683813, z: 1, w: 0.000009986938}
  m_LocalPosition: {x: 0.057560336, y: 0.018850708, z: 0.000037461232}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1256160826407134159}
  m_Father: {fileID: 7040960904282605691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7043023468680383296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 653180301364527254}
  m_Layer: 0
  m_Name: CameraPosition
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &653180301364527254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7043023468680383296}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.45, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7088407481792215657
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 753572775222062678}
  - component: {fileID: 5866401820140534889}
  m_Layer: 0
  m_Name: CC_Base_Tongue
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &753572775222062678
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7088407481792215657}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5866401820140534889
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7088407481792215657}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e053034ff17f24c4786eea07a8c73342, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1654403436102926152, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 2102168643567736953}
  - {fileID: 4778733340781212292}
  - {fileID: 6332157594019442039}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 6332157594019442039}
  m_AABB:
    m_Center: {x: -0.03286753, y: 0.003165951, z: -0.00006536394}
    m_Extent: {x: 0.054333396, y: 0.029733827, z: 0.025414914}
  m_DirtyAABB: 0
--- !u!1 &7113660547249328270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 991082308300171921}
  - component: {fileID: 7286677389078680567}
  m_Layer: 0
  m_Name: SphereDetectSensor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &991082308300171921
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7113660547249328270}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5729056741835728292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7286677389078680567
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7113660547249328270}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b9ba30f9034691542b9ec15a5937cc06, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  LastPosition: {x: 0, y: 0, z: 0}
  m_collider: {fileID: 0}
  cooldownTime: 2
  canDetect: 1
  canLose: 1
  detectedObjects: []
  m_aimingModule: {fileID: 7029741597687228658}
  m_characterParameters: {fileID: 3336923530583100265}
--- !u!1 &7129662479146005823
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3316849888583807694}
  m_Layer: 0
  m_Name: CC_Base_L_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3316849888583807694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7129662479146005823}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001101011, y: 0.6153887, z: 0.7882219, w: -0.0013287836}
  m_LocalPosition: {x: -0.098425545, y: 0.047770996, z: 0.14246082}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6354417209521280469}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7133708695093245624
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6943016194066793060}
  m_Layer: 0
  m_Name: MouseLookTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6943016194066793060
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7133708695093245624}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 12.53}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7530810731390684953}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7195047958775627785
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4254692493541640014}
  m_Layer: 0
  m_Name: neck_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4254692493541640014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7195047958775627785}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
  m_LocalPosition: {x: 0.0000000077486035, y: 0.26940826, z: -0.0000020599364}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1975774117448653}
  - {fileID: 7332317681643830073}
  m_Father: {fileID: 6354417209521280469}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7293884257765008373
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1604281555635059624}
  m_Layer: 0
  m_Name: clavicle_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1604281555635059624
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7293884257765008373}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0058371807, y: -0.17127486, z: 0.6711394, w: 0.72125083}
  m_LocalPosition: {x: -0.04634696, y: 0.2128839, z: 0.004223671}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4746873980746277075}
  m_Father: {fileID: 6354417209521280469}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7297851167552017096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7892479749728525563}
  - component: {fileID: 8931926817661797498}
  - component: {fileID: 6239631413643935259}
  - component: {fileID: 7788491851095697022}
  m_Layer: 7
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7892479749728525563
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7297851167552017096}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0051458613, y: -0.00012410936, z: 0.024217188, w: 0.99969345}
  m_LocalPosition: {x: -0.0013655052, y: 0.5098188, z: -0.000010344519}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2641471771616559337}
  m_Father: {fileID: 7443338930424432770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &8931926817661797498
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7297851167552017096}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &6239631413643935259
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7297851167552017096}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09310645
  m_Height: 0.5209739
  m_Direction: 1
  m_Center: {x: -0.00000020768493, y: 0.2468812, z: -0.00000020070004}
--- !u!153 &7788491851095697022
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7297851167552017096}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 2044566165639447891}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -3.0163608
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 136.98364
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7346037527402848085
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3696230167568432270}
  m_Layer: 0
  m_Name: calf_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3696230167568432270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7346037527402848085}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000035669185, y: -0.000019150324, z: -0.000027639298, w: 1}
  m_LocalPosition: {x: 0.00000021934508, y: 0.24685065, z: -0.00000035762787}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5932891594977931567}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7366418819176025263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7530810731390684953}
  m_Layer: 0
  m_Name: MouseLookCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7530810731390684953
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7366418819176025263}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.303585, z: 0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6943016194066793060}
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7391173183968725619
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 752559713370163875}
  - component: {fileID: 6157800066689247}
  - component: {fileID: 787269354070660450}
  m_Layer: 0
  m_Name: QuadTextured
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &752559713370163875
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7391173183968725619}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7099972, y: 0, z: 0, w: 0.70420456}
  m_LocalPosition: {x: 0, y: 0.1, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5066961399519885503}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6157800066689247
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7391173183968725619}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &787269354070660450
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7391173183968725619}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5f21e3dbd08bcea418f24a23a59f4cb3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7424690506623681393
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1087875349283475642}
  - component: {fileID: 6933222966420822129}
  m_Layer: 6
  m_Name: LimbIK Left Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1087875349283475642
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7424690506623681393}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3338510557000658300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6933222966420822129
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7424690506623681393}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4db3c450680fd4c809d5ad90a2f24e5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 0.67650366, y: 1.5153742, z: -0.062369492}
    IKPositionWeight: 0
    root: {fileID: 1087875349283475642}
    target: {fileID: 0}
    IKRotationWeight: 0
    IKRotation: {x: 0.073177755, y: -0.07616624, z: -0.6849927, w: 0.7208529}
    bendNormal: {x: -0.000006117496, y: -0.0000000059905942, z: 0.000983794}
    bone1:
      transform: {fileID: 1570984535673114288}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.00000015258789, y: 0.100599535, z: -0.0000004816055}
      defaultLocalRotation: {x: 0.117331214, y: -0.0024746358, z: -0.027261361, w: 0.99271554}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone2:
      transform: {fileID: 800051801862851842}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.0000007629394, y: 0.28141707, z: 0.0000009012222}
      defaultLocalRotation: {x: 0.0000016458846, y: 0.000018592604, z: 0.0073306826,
        w: 0.9999731}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone3:
      transform: {fileID: 6703786042559524125}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.24901588, z: 0.0000001335144}
      defaultLocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    goal: 2
    bendModifier: 0
    maintainRotationWeight: 0
    bendModifierWeight: 1
    bendGoal: {fileID: 0}
--- !u!1 &7489697247530677356
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5066961399519885503}
  m_Layer: 0
  m_Name: CompassArrow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5066961399519885503
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7489697247530677356}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 1.707269}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 752559713370163875}
  - {fileID: 3859776997442730159}
  m_Father: {fileID: 7329253386303068935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7509254049099939209
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 893050845294910504}
  m_Layer: 21
  m_Name: Mesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &893050845294910504
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7509254049099939209}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5834859071013931074}
  - {fileID: 4359108457390472948}
  - {fileID: 7969376479293448957}
  - {fileID: 2797939799877729073}
  - {fileID: 4458265800915109053}
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7528689399865443625
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5695610274983953874}
  m_Layer: 0
  m_Name: CC_Base_R_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5695610274983953874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7528689399865443625}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5000003, y: -0.49999976, z: -0.5000003, w: -0.49999976}
  m_LocalPosition: {x: 0.062119015, y: 0.05875595, z: -0.032109845}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7040960904282605691}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7596473977709155805
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8197213499133234792}
  m_Layer: 11
  m_Name: FloorAngleHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8197213499133234792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7596473977709155805}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: -0.00000016292068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 650870691778268658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7750339412140048412
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1821585893695261248}
  - component: {fileID: 532645659814311735}
  - component: {fileID: 8472728651262294697}
  m_Layer: 6
  m_Name: Aim Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1821585893695261248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7750339412140048412}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3338510557000658300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &532645659814311735
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7750339412140048412}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8472728651262294697
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7750339412140048412}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aae886dd0d5d59844b4ec40cc2d96918, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7807136422895353689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1977733543387358763}
  m_Layer: 0
  m_Name: upperarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1977733543387358763
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7807136422895353689}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000017129787, y: 0.00000023047708, z: -0.0000014104878,
    w: 1}
  m_LocalPosition: {x: -0.00000030517577, y: 0.14066535, z: -0.0000000047683715}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4746873980746277075}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7846032607265209615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 779325286296278069}
  - component: {fileID: 9158100504275654090}
  - component: {fileID: 86550668548316080}
  m_Layer: 19
  m_Name: CoverIndicator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &779325286296278069
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7846032607265209615}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.7071068, z: -0.7071068, w: 0}
  m_LocalPosition: {x: 0, y: 0.858, z: 0.525}
  m_LocalScale: {x: 0.20951428, y: 1.373864, z: 0.23819567}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 90, y: 180, z: 0}
--- !u!33 &9158100504275654090
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7846032607265209615}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &86550668548316080
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7846032607265209615}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1450695b1dfa13f44b767188c630ffdd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7851328254261305158
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1143483047653081985}
  m_Layer: 0
  m_Name: middle_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1143483047653081985
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7851328254261305158}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07578812, y: 0.0029155503, z: 0.04082293, w: 0.99628365}
  m_LocalPosition: {x: -0, y: 0.09293022, z: 0.000000038146972}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7272893972138136223}
  m_Father: {fileID: 5383975135103560389}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7859181863832922339
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4458265800915109053}
  - component: {fileID: 7441146910546910347}
  m_Layer: 21
  m_Name: trigger 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4458265800915109053
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7859181863832922339}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 893050845294910504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7441146910546910347
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7859181863832922339}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8050392602797321999, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 1383474775041360180}
  - {fileID: 3059993682524517977}
  - {fileID: 3925261838396088709}
  - {fileID: 4046584383438219079}
  - {fileID: 5284782164835845971}
  - {fileID: 6938360488027384646}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3925261838396088709}
  m_AABB:
    m_Center: {x: 0, y: -0.052191965, z: 0.045141164}
    m_Extent: {x: 0.003946688, y: 0.0154629145, z: 0.010327298}
  m_DirtyAABB: 0
--- !u!1 &7895315661048287339
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7471355865723362730}
  - component: {fileID: 5572294579708353132}
  m_Layer: 21
  m_Name: Pistol
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7471355865723362730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7895315661048287339}
  serializedVersion: 2
  m_LocalRotation: {x: -0.467751, y: -0.45687014, z: -0.53196406, w: 0.5380456}
  m_LocalPosition: {x: 0.034, y: 0.155, z: -0.007}
  m_LocalScale: {x: 1.0017674, y: 1.0290117, z: 1.0127568}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 520379800261747354}
  - {fileID: 1410949846053669376}
  m_Father: {fileID: 6703786042559524125}
  m_LocalEulerAnglesHint: {x: -81.658, y: 2.378, z: -91.403}
--- !u!114 &5572294579708353132
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7895315661048287339}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a0b21ec855a7db42ab30d4665de8769, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  <CurrentAmmo>k__BackingField: 0
  <FireRate>k__BackingField: 0
  <NextFireTime>k__BackingField: 0
  <DamagePerShoot>k__BackingField: 0
  <Health>k__BackingField: 0
  <CanAim>k__BackingField: 0
  <CanReload>k__BackingField: 0
  <IsAiming>k__BackingField: 0
  <IsShooting>k__BackingField: 0
  <IsReloading>k__BackingField: 0
  <AimPointer>k__BackingField: {fileID: 8541219829470538690}
  <Accuracy>k__BackingField: 0.8
  <MaxRange>k__BackingField: 50
  <DamageAmount>k__BackingField: 25
  <SpreadAngle>k__BackingField: 5
  <RecoilAmount>k__BackingField: 1
  <RecoilRecovery>k__BackingField: 0.5
  <CurrentRecoil>k__BackingField: 0
  <LastShotTime>k__BackingField: 0
--- !u!1 &7931562027539279582
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3975190985635198000}
  m_Layer: 0
  m_Name: middle_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3975190985635198000
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7931562027539279582}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01329394, y: 0.0036442443, z: -0.006823158, w: 0.99988174}
  m_LocalPosition: {x: -0.00000061035155, y: 0.044478226, z: -0.0000003147125}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7323052689804063343}
  m_Father: {fileID: 1316463068385753957}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8177101847407332856
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8451639263143781937}
  - component: {fileID: 4001217664900525313}
  - component: {fileID: 8542744944642605514}
  m_Layer: 7
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8451639263143781937
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177101847407332856}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242954, y: 0.072389625, z: -0.036033686, w: 0.96155715}
  m_LocalPosition: {x: -0.00005637108, y: 0.5252105, z: 0.00032638057}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 123275233525388870}
  m_Father: {fileID: 6393905998216405920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &4001217664900525313
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177101847407332856}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &8542744944642605514
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8177101847407332856}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8714427012979128679}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &8197699010481665106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7041930962628465281}
  m_Layer: 0
  m_Name: CompassArrow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7041930962628465281
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8197699010481665106}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 1.2788713}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6135183471443248035}
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8199526439275725846
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9086886816831505705}
  - component: {fileID: 8830726950650311852}
  - component: {fileID: 9195335255939506981}
  - component: {fileID: 5954690637389537017}
  m_Layer: 0
  m_Name: AnimationStates
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9086886816831505705
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8199526439275725846}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4031297349607152135}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8830726950650311852
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8199526439275725846}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc8f0de9be674791873dab2408c742ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 7400000, guid: 37387e3558950f846884db1269793fed, type: 2}
    - {fileID: 7400002, guid: 459a127509d869b40908dffa197da5fa, type: 3}
    - {fileID: 7400000, guid: 2eca9b0c87f01c843903863e6d00a38d, type: 2}
    - {fileID: 7400000, guid: cb7af1a91c92e584697c05ddcffa8f85, type: 2}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: ConditionalAnimations
      Entry: 6
      Data: 
    - Name: <weaponsIdleAnimations>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[WeaponSubModuleState, General],[System.Collections.Generic.Dictionary`2[[WeaponSubState,
        General],[Animancer.ClipTransition, Animancer]], mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubModuleState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 0
    - Name: $v
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[WeaponSubState, General],[Animancer.ClipTransition,
        Animancer]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 3|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 2
    - Name: $v
      Entry: 7
      Data: 4|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 5|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 6|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 7|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 8|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 0
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 3
    - Name: $v
      Entry: 7
      Data: 9|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 10|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 11|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 12|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 13|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 1
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 5
    - Name: $v
      Entry: 7
      Data: 14|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 15|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 16|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 17|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 18|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 2
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 1
    - Name: $v
      Entry: 7
      Data: 19|System.Collections.Generic.Dictionary`2[[WeaponSubState, General],[Animancer.ClipTransition,
        Animancer]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 20|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 2
    - Name: $v
      Entry: 7
      Data: 21|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 22|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 23|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 24|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 25|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 3
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 3
    - Name: $v
      Entry: 7
      Data: 26|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 27|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 28|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 29|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 30|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 6
      Data: 
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  _Character: {fileID: 3387135692686593795}
  _MainAnimation:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400002, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  _FirstRandomizeDelay: 5
  _MinRandomizeInterval: 0
  _MaxRandomizeInterval: 20
  _normalAnimations: []
--- !u!114 &9195335255939506981
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8199526439275725846}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 158ab36da7ed9dd449fd0093d07064d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: ConditionalAnimations
      Entry: 6
      Data: 
    - Name: m_float1Controller
      Entry: 6
      Data: 
  _Character: {fileID: 3387135692686593795}
  rotationSpeed: 10
  vertical: 0
  horizontal: 0
  verticalDump: 0.25
  horizontalDump: 0.25
  inputAngle: 0
  inputMagnitude: 0
  inputMagnitudeDamp: 0.1
  InputAngleDump: 0
  InputAngleDumpA: 0.25
  InputAngleDumpB: 2
  InputAngleDumpT: 2
  m_debugParameterVector: {x: 0, y: 0}
  m_isMoving: 0
  m_fadeTime: 0.5
  m_NormalMovementAnimator: {fileID: 9100000, guid: 7122b14c8eac0a9488c25f928f30ec05,
    type: 2}
  m_normalWalkingBlendTree:
    StartWalkingBlendTree:
      _Asset: {fileID: 11400000, guid: f5a5b26c5309f3a448fdb4886f68c46a, type: 2}
    WalkingBlendTree:
      _Asset: {fileID: 11400000, guid: 53da9a148d697da46bf5b1b4cbfa7291, type: 2}
  m_strufWalkingBlendTree:
    StartWalkingBlendTree:
      _Asset: {fileID: 11400000, guid: 7714c8c08316bc044a7b8c38221ed939, type: 2}
    WalkingBlendTree:
      _Asset: {fileID: 11400000, guid: 73ff0b4602c1c9744979b3891ffef3ab, type: 2}
  m_weaponMovementMixers: []
  m_characterTransform: {fileID: 1768001276132255913}
  _QuickTurnMoveSpeed: 2
  _QuickTurnAngle: 145
  _canUpdate: 0
  m_footstepEventsAnimation: {fileID: 4819793649072361018}
  normalMovementResponseTime: 0.12
  aimingMovementResponseTime: 0.08
  startMovementBlendTime: 0.15
  directionChangeBlendTime: 0.2
  magnitudeChangeBlendTime: 0.1
  directionChangeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  footstepSpeedInfluence: 0.2
  _PlayFootstepAudio:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &5954690637389537017
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8199526439275725846}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030557ec1fa0482e8e7af6244d95fd88, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 7400000, guid: f1a68184ce21e0d4fbce92973dc0363b, type: 2}
    - {fileID: 7400000, guid: 535f40ff3bb6b3349870fab03a5acfe4, type: 2}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: ConditionalAnimations
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[Animancer.ClipTransition,
        Animancer]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AimToNormalLeft
    - Name: $v
      Entry: 7
      Data: 2|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 3|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 4|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 5|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 6|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 0
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AimToNormalRight
    - Name: $v
      Entry: 7
      Data: 7|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 8|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 9|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 10|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 11|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 1
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  _Character: {fileID: 3387135692686593795}
  m_aimToNormal: 0
  _stopLeftBlendTreeAsset:
    _Asset: {fileID: 11400000, guid: 10359f3c59f7edd4db227abc226669b7, type: 2}
  _stopRightBlendTreeAsset:
    _Asset: {fileID: 11400000, guid: 82f9a2719f8c37d45a4ac17e833baa88, type: 2}
  vertical: 0
  horizontal: 0
  stopAnimationSpeedMultiplier: 2
  highSpeedStopMultiplier: 1.2
  aimingStopMultiplier: 0.9
  directionChangeInfluence: 0.3
  blendDuration: 0.15
--- !u!1 &8244690496138323540
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8953174486455695084}
  m_Layer: 0
  m_Name: RaycasterFists
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8953174486455695084
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8244690496138323540}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8330809232213530030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2463105333083752492}
  m_Layer: 0
  m_Name: VaultCharRot_Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2463105333083752492
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8330809232213530030}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 2.27}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6650104368019110041}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8388806756435205330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5932891594977931567}
  m_Layer: 0
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5932891594977931567
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8388806756435205330}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0054815323, y: 0.0001228251, z: -0.022256004, w: 0.99973726}
  m_LocalPosition: {x: -0.000000538826, y: 0.47930038, z: -0.000000629425}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3696230167568432270}
  - {fileID: 2302558780381168062}
  m_Father: {fileID: 1246016646434924394}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8542001876265042753
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2302558780381168062}
  m_Layer: 0
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2302558780381168062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8542001876265042753}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242954, y: 0.072389625, z: -0.036033686, w: 0.96155715}
  m_LocalPosition: {x: 0.00000044345856, y: 0.49370125, z: -0.00000071048737}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9184654208057938550}
  - {fileID: 478716012185291693}
  m_Father: {fileID: 5932891594977931567}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8544686208584351649
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7113810491443191821}
  m_Layer: 0
  m_Name: ball_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7113810491443191821
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8544686208584351649}
  serializedVersion: 2
  m_LocalRotation: {x: 0.47113988, y: 0.020058213, z: -0.025840553, w: 0.8814517}
  m_LocalPosition: {x: 0.0000048828124, y: 0.14396666, z: 0.0000016212463}
  m_LocalScale: {x: 0.9999997, y: 0.99999964, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1771932099999606124}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8603609933891056864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2210427295702728430}
  m_Layer: 0
  m_Name: VaultDirBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2210427295702728430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8603609933891056864}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.99771255, z: 0, w: 0.06759941}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.25699458, y: 0.2569946, z: 0.25699458}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6650104368019110041}
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8612707793424828930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3146388501999888161}
  - component: {fileID: 6859376943747118146}
  - component: {fileID: 1936662911007915642}
  - component: {fileID: 5232757845509304316}
  m_Layer: 7
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3146388501999888161
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8612707793424828930}
  serializedVersion: 2
  m_LocalRotation: {x: -0.037495185, y: -0.000004858333, z: -0.00009620549, w: 0.99929684}
  m_LocalPosition: {x: 0.00011033542, y: 0.4792034, z: -0.07086896}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6411998766675819667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &6859376943747118146
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8612707793424828930}
  serializedVersion: 5
  m_Mass: 5.4900002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &1936662911007915642
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8612707793424828930}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.14306502
  m_Height: 0.26228586
  m_Direction: 1
  m_Center: {x: 0.00015779182, y: 0.06544352, z: -0.014051062}
--- !u!153 &5232757845509304316
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8612707793424828930}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 511570501894979341}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &8724504895924678628
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5834859071013931074}
  - component: {fileID: 5071179120757683629}
  m_Layer: 21
  m_Name: body 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5834859071013931074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8724504895924678628}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 893050845294910504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5071179120757683629
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8724504895924678628}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 6dac53837b982ba498a9e3bb2465d74f, type: 3}
  m_Bones:
  - {fileID: 1383474775041360180}
  - {fileID: 3059993682524517977}
  - {fileID: 3925261838396088709}
  - {fileID: 4046584383438219079}
  - {fileID: 5284782164835845971}
  - {fileID: 6938360488027384646}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 3925261838396088709}
  m_AABB:
    m_Center: {x: -0.00033851434, y: -0.07057169, z: 0.058352485}
    m_Extent: {x: 0.014922695, y: 0.07614103, z: 0.115497485}
  m_DirtyAABB: 0
--- !u!1 &8739159975338932085
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4245204652383740398}
  m_Layer: 0
  m_Name: index_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4245204652383740398
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8739159975338932085}
  serializedVersion: 2
  m_LocalRotation: {x: -0.015389408, y: 0.000277427, z: 0.005215452, w: 0.999868}
  m_LocalPosition: {x: -0.0004373169, y: 0.028221816, z: -0.00072290417}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5269145932365844873}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8761676093830047013
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3587060510133478845}
  - component: {fileID: 2506931397602712867}
  - component: {fileID: 7624126448212688277}
  - component: {fileID: 5556824234679884214}
  m_Layer: 7
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3587060510133478845
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8761676093830047013}
  serializedVersion: 2
  m_LocalRotation: {x: -0.026243053, y: 0.026773466, z: 0.71181405, w: 0.7013668}
  m_LocalPosition: {x: -0.14535676, y: 0.35251522, z: -0.089556724}
  m_LocalScale: {x: 0.99999976, y: 0.9999998, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4214021402500490547}
  m_Father: {fileID: 6411998766675819667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &2506931397602712867
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8761676093830047013}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &7624126448212688277
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8761676093830047013}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0861205
  m_Height: 0.31577516
  m_Direction: 1
  m_Center: {x: 0.0000002495945, y: 0.14066541, z: -0.0000000111758744}
--- !u!153 &5556824234679884214
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8761676093830047013}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 511570501894979341}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &8770325287295613875
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5126402618727581682}
  - component: {fileID: 1314107655064283364}
  m_Layer: 0
  m_Name: Ines_Default_Gloves
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5126402618727581682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8770325287295613875}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1314107655064283364
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8770325287295613875}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 622c22f36d9b9c94d89d6f00a899b550, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 279771734379647476, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 4689124444566144165}
  - {fileID: 2898215844599901242}
  - {fileID: 5383975135103560389}
  - {fileID: 8657857344066895426}
  - {fileID: 4127082261127725367}
  - {fileID: 1143483047653081985}
  - {fileID: 5009751097223364029}
  - {fileID: 4932665124173280898}
  - {fileID: 7839827620740900589}
  - {fileID: 3359683039249218388}
  - {fileID: 1390068458671367855}
  - {fileID: 5269145932365844873}
  - {fileID: 7272893972138136223}
  - {fileID: 3736384074916615092}
  - {fileID: 1812205661720675985}
  - {fileID: 6703786042559524125}
  - {fileID: 6722320523965547818}
  - {fileID: 6783681568393303306}
  - {fileID: 1316463068385753957}
  - {fileID: 7816194994385151601}
  - {fileID: 1027531031248273751}
  - {fileID: 318315931718969326}
  - {fileID: 800051801862851842}
  - {fileID: 7556605754010412423}
  - {fileID: 8007648465939198288}
  - {fileID: 3975190985635198000}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 800051801862851842}
  m_AABB:
    m_Center: {x: 0.0060192235, y: -0.42701393, z: 0.025629144}
    m_Extent: {x: 0.041894034, y: 0.81338686, z: 0.07448232}
  m_DirtyAABB: 0
--- !u!1 &8878326531334946622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 37560226843851422}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &37560226843851422
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8878326531334946622}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.0000029843027, y: 0, z: -0.03379729}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6809340320723751594}
  m_Father: {fileID: 1768001276132255913}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8925216325680607653
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4689124444566144165}
  m_Layer: 0
  m_Name: ring_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4689124444566144165
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8925216325680607653}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0840788, y: -0.0007057925, z: 0.03980939, w: 0.99566334}
  m_LocalPosition: {x: -0.0015324402, y: 0.088897474, z: -0.016537819}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2898215844599901242}
  m_Father: {fileID: 5383975135103560389}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8994505558729763478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1027531031248273751}
  m_Layer: 0
  m_Name: index_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1027531031248273751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8994505558729763478}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07187013, y: -0.0035265917, z: -0.040508132, w: 0.99658483}
  m_LocalPosition: {x: 0.0016203307, y: 0.09508155, z: 0.017892266}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8007648465939198288}
  m_Father: {fileID: 6703786042559524125}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9050491164790984351
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3714135272472090369}
  - component: {fileID: 2897704043420552342}
  - component: {fileID: 7330618670432837630}
  m_Layer: 2
  m_Name: VaultDetector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3714135272472090369
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9050491164790984351}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.416, z: 0.574}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6975277655420141540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &2897704043420552342
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9050491164790984351}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.11
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &7330618670432837630
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9050491164790984351}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &9128302662376098774
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7040960904282605691}
  m_Layer: 0
  m_Name: CC_Base_FacialBone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7040960904282605691
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9128302662376098774}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000017471124, y: -0.7071068, z: -0.0000017471124, w: 0.7071068}
  m_LocalPosition: {x: 0.0000000026170164, y: 0.00000030517577, z: 0.000000026226044}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4207601964448435926}
  - {fileID: 7309246504749364867}
  - {fileID: 5695610274983953874}
  - {fileID: 8895518437609776394}
  m_Father: {fileID: 1975774117448653}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9131449353465375496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3484308106543394247}
  m_Layer: 21
  m_Name: muzzle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3484308106543394247
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9131449353465375496}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.051890533, z: 0.13181114}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 520379800261747354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9144632688756932070
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1975774117448653}
  m_Layer: 0
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1975774117448653
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9144632688756932070}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14982727, y: 0.00070315227, z: -0.0061363596, w: 0.98869294}
  m_LocalPosition: {x: -0.000000023841856, y: 0.071730494, z: 0.000000038146972}
  m_LocalScale: {x: 1.0000001, y: 0.9999996, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7040960904282605691}
  m_Father: {fileID: 4254692493541640014}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9155379122873702795
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8061476685868375142}
  m_Layer: 0
  m_Name: pinky_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8061476685868375142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9155379122873702795}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0072581605, y: 0.000016972777, z: -0.05006522, w: 0.9987196}
  m_LocalPosition: {x: -0.00000015258789, y: 0.016174164, z: -0.000000057220458}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6783681568393303306}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9157401359471383304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3315732286828748650}
  m_Layer: 0
  m_Name: calf_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3315732286828748650
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9157401359471383304}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000016143078, y: 0.0001604101, z: 0.000012576289, w: 1}
  m_LocalPosition: {x: -0.00000021934508, y: 0.24688132, z: -0.0000002670288}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 784631101705099529}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &4655092384149536701
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7471355865723362730}
    m_Modifications:
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_RootOrder
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.9897865
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 1.0257374
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.9850558
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.052045982
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.013365984
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.0012076038
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5562499
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.575913
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.3550611
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.48253703
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -39.064
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -117.272
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -60.013
      objectReference: {fileID: 0}
    - target: {fileID: 6360584711328263943, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_Name
      value: SciFi_Pistol_4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2820369101759257821}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 8478060350893148715}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 8541219829470538690}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4357931742740439138}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c900257853a7a74f9b11e31d554c873, type: 3}
--- !u!4 &1410949846053669376 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
    type: 3}
  m_PrefabInstance: {fileID: 4655092384149536701}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7106052305035234864
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 520379800261747354}
    m_Modifications:
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.0443
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.1122
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.99732536
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.043009184
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.0132904295
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.05758212
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Name
      value: AimPointer
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c513e3cb59a72543b45a4c740779f56, type: 3}
--- !u!4 &7294275782284248493 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
    type: 3}
  m_PrefabInstance: {fileID: 7106052305035234864}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8155807746326536287
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1410949846053669376}
    m_Modifications:
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 1.0152811
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9751786
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 1.0099491
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.13600597
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.020960063
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.0021262132
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70930266
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.01348043
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70448285
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.020296047
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Name
      value: AimPointer
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c513e3cb59a72543b45a4c740779f56, type: 3}
--- !u!4 &8541219829470538690 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
    type: 3}
  m_PrefabInstance: {fileID: 8155807746326536287}
  m_PrefabAsset: {fileID: 0}
