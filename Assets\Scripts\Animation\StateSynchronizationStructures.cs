using System;
using System.Collections.Generic;
using UnityEngine;

namespace Animation.StateSync
{
    /// <summary>
    /// Information about state synchronization events
    /// </summary>
    [Serializable]
    public struct StateSynchronizationInfo
    {
        public UnifiedAnimationState PreviousState;
        public UnifiedAnimationState NewState;
        public StateChangeComponents ChangedComponents;
        public float SynchronizationTime;
        public int TotalChanges;
        
        public override string ToString()
        {
            return $"StateSyncInfo[Changes:{TotalChanges}, Time:{SynchronizationTime:F3}s, " +
                   $"Components:{ChangedComponents}]";
        }
    }
    
    /// <summary>
    /// Tracks which state components have changed
    /// </summary>
    [Serializable]
    public struct StateChangeComponents
    {
        public bool MainStateChanged;
        public bool MovementStateChanged;
        public bool WeaponStateChanged;
        public bool AnimationStateChanged;
        
        public int ChangeCount => 
            (MainStateChanged ? 1 : 0) + 
            (MovementStateChanged ? 1 : 0) + 
            (WeaponStateChanged ? 1 : 0) + 
            (AnimationStateChanged ? 1 : 0);
            
        public override string ToString()
        {
            var changes = new List<string>();
            if (MainStateChanged) changes.Add("Main");
            if (MovementStateChanged) changes.Add("Movement");
            if (WeaponStateChanged) changes.Add("Weapon");
            if (AnimationStateChanged) changes.Add("Animation");
            return string.Join(",", changes);
        }
    }
    
    /// <summary>
    /// State validation error information
    /// </summary>
    [Serializable]
    public struct StateValidationError
    {
        public UnifiedAnimationState PreviousState;
        public UnifiedAnimationState AttemptedState;
        public string ErrorMessage;
        public float Timestamp;
        
        public override string ToString()
        {
            return $"ValidationError[{ErrorMessage}] at {Timestamp:F3}s";
        }
    }
    
    /// <summary>
    /// State validation result
    /// </summary>
    [Serializable]
    public struct StateValidationResult
    {
        public bool IsValid;
        public string ErrorMessage;
        public bool ShouldRevert;
        public UnifiedAnimationState CorrectedState;
        
        public static StateValidationResult Valid => new StateValidationResult
        {
            IsValid = true,
            ErrorMessage = string.Empty,
            ShouldRevert = false
        };
        
        public static StateValidationResult Invalid(string error, bool shouldRevert = false)
        {
            return new StateValidationResult
            {
                IsValid = false,
                ErrorMessage = error,
                ShouldRevert = shouldRevert
            };
        }
    }
    
    /// <summary>
    /// Statistics about state synchronization
    /// </summary>
    [Serializable]
    public struct StateSynchronizationStats
    {
        public int TotalStateChanges;
        public int SynchronizationUpdates;
        public Dictionary<Type, int> EventCounts;
        public float LastSynchronizationTime;
        public UnifiedAnimationState CurrentUnifiedState;
        
        public float AverageEventsPerSecond => 
            LastSynchronizationTime > 0 ? TotalStateChanges / LastSynchronizationTime : 0f;
            
        public override string ToString()
        {
            return $"SyncStats[Changes:{TotalStateChanges}, Updates:{SynchronizationUpdates}, " +
                   $"AvgPerSec:{AverageEventsPerSecond:F1}]";
        }
    }
    
    /// <summary>
    /// State validation rules for ensuring valid state transitions
    /// </summary>
    public class StateValidationRules
    {
        private readonly Dictionary<string, Func<UnifiedAnimationState, UnifiedAnimationState, bool>> validationRules;
        
        public StateValidationRules()
        {
            validationRules = new Dictionary<string, Func<UnifiedAnimationState, UnifiedAnimationState, bool>>
            {
                ["NoShootingWithEmptyHands"] = (prev, next) => 
                    !(next.weaponState == WeaponSubState.Shooting && next.weaponModuleState == WeaponSubModuleState.EmptyHand),
                    
                ["AnimationWeaponConsistency"] = (prev, next) => 
                    !(next.animationState == AnimationSubState.Shooting && next.weaponState != WeaponSubState.Shooting),
                    
                ["ValidMovementTransitions"] = (prev, next) => 
                    IsValidMovementTransition(prev.movementState, next.movementState),
                    
                ["ValidWeaponTransitions"] = (prev, next) => 
                    IsValidWeaponTransition(prev.weaponState, next.weaponState),
                    
                ["NoSimultaneousConflicts"] = (prev, next) => 
                    !HasSimultaneousConflicts(next)
            };
        }
        
        /// <summary>
        /// Validate a state transition
        /// </summary>
        public StateValidationResult ValidateStateTransition(UnifiedAnimationState previous, UnifiedAnimationState next)
        {
            foreach (var rule in validationRules)
            {
                if (!rule.Value(previous, next))
                {
                    return StateValidationResult.Invalid($"Validation rule '{rule.Key}' failed", true);
                }
            }
            
            return StateValidationResult.Valid;
        }
        
        /// <summary>
        /// Check if movement transition is valid
        /// </summary>
        private bool IsValidMovementTransition(MovementSubState from, MovementSubState to)
        {
            // Allow same-state transitions (no change)
            if (from == to) return true;

            // Define invalid transitions (but allow target loss scenarios)
            var invalidTransitions = new HashSet<(MovementSubState, MovementSubState)>
            {
                // Can't go directly from Standing to Stop (unless stopping)
                (MovementSubState.Standing, MovementSubState.Stop)
                // Removed WalkingWithTurn -> Standing restriction to allow target loss transitions
            };

            return !invalidTransitions.Contains((from, to));
        }
        
        /// <summary>
        /// Check if weapon transition is valid
        /// </summary>
        private bool IsValidWeaponTransition(WeaponSubState from, WeaponSubState to)
        {
            // Define invalid transitions
            var invalidTransitions = new HashSet<(WeaponSubState, WeaponSubState)>
            {
                // Can't go directly from Idle to Shooting without Aiming
                (WeaponSubState.Idle, WeaponSubState.Shooting),
                // Can't go from UnEquipping to Shooting
                (WeaponSubState.UnEquipping, WeaponSubState.Shooting)
            };
            
            return !invalidTransitions.Contains((from, to));
        }
        
        /// <summary>
        /// Check for simultaneous conflicts in state
        /// </summary>
        private bool HasSimultaneousConflicts(UnifiedAnimationState state)
        {
            // Check for conflicting states
            if (state.movementState == MovementSubState.Stop && state.weaponState == WeaponSubState.Shooting)
            {
                return true; // Can't shoot while stopping
            }
            
            if (state.animationState == AnimationSubState.Dying && 
                (state.weaponState == WeaponSubState.Shooting || state.movementState != MovementSubState.Standing))
            {
                return true; // Can't do other actions while dying
            }
            
            return false;
        }
    }
}

namespace Events
{
    /// <summary>
    /// Event fired when unified animation state changes
    /// </summary>
    public struct OnUnifiedStateChangedEvent
    {
        public Animation.StateSync.UnifiedAnimationState PreviousState;
        public Animation.StateSync.UnifiedAnimationState NewState;
        public float Timestamp;
        
        public OnUnifiedStateChangedEvent(Animation.StateSync.UnifiedAnimationState previous, 
            Animation.StateSync.UnifiedAnimationState newState)
        {
            PreviousState = previous;
            NewState = newState;
            Timestamp = UnityEngine.Time.time;
        }
        
        public override string ToString()
        {
            return $"UnifiedStateChanged[{PreviousState.ToCompactString()} → {NewState.ToCompactString()}]";
        }
    }
    
    /// <summary>
    /// Event fired when animation state changes (for AnimationSubState specifically)
    /// </summary>
    public struct OnAnimationStateChangedEvent
    {
        public AnimationSubState PreviousState;
        public AnimationSubState NewState;
        public float Timestamp;
        
        public OnAnimationStateChangedEvent(AnimationSubState previous, AnimationSubState newState)
        {
            PreviousState = previous;
            NewState = newState;
            Timestamp = UnityEngine.Time.time;
        }
        
        public override string ToString()
        {
            return $"AnimationStateChanged[{PreviousState} → {NewState}]";
        }
    }
    
    /// <summary>
    /// Event fired when state synchronization occurs
    /// </summary>
    public struct OnStateSynchronizedEvent
    {
        public Animation.StateSync.StateSynchronizationInfo SynchronizationInfo;
        public float Timestamp;
        
        public OnStateSynchronizedEvent(Animation.StateSync.StateSynchronizationInfo syncInfo)
        {
            SynchronizationInfo = syncInfo;
            Timestamp = UnityEngine.Time.time;
        }
        
        public override string ToString()
        {
            return $"StateSynchronized[{SynchronizationInfo}]";
        }
    }
    
    /// <summary>
    /// Event fired when state validation fails
    /// </summary>
    public struct OnStateValidationFailedEvent
    {
        public Animation.StateSync.StateValidationError ValidationError;
        public float Timestamp;
        
        public OnStateValidationFailedEvent(Animation.StateSync.StateValidationError error)
        {
            ValidationError = error;
            Timestamp = UnityEngine.Time.time;
        }
        
        public override string ToString()
        {
            return $"ValidationFailed[{ValidationError}]";
        }
    }
}
