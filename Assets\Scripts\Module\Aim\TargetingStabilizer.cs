using System.Collections;
using UnityEngine;
using Events;
using Module.Mono.Animancer.RealsticFemale;

namespace Module.Aim
{
    /// <summary>
    /// Stabilizes targeting after enemy death to prevent rapid state changes
    /// and improve target selection when enemies surround the player
    /// </summary>
    public class TargetingStabilizer : MonoBehaviour
    {
        [Header("Stabilization Settings")]
        [SerializeField] private float targetDeathStabilizationDelay = 0.3f;
        [SerializeField] private float targetSelectionCooldown = 0.2f;
        [SerializeField] private bool enableDebugLogging = false;
        
        [Header("Current State")]
        [SerializeField] private bool isStabilizing = false;
        [SerializeField] private bool isInSelectionCooldown = false;
        [SerializeField] private float stabilizationEndTime = 0f;
        [SerializeField] private float selectionCooldownEndTime = 0f;
        
        private AimingModule aimingModule;
        private bool wasAimingBeforeStabilization = false;
        
        private void Awake()
        {
            aimingModule = GetComponent<AimingModule>();
            if (aimingModule == null)
            {
                Debug.LogError("[TargetingStabilizer] AimingModule not found on the same GameObject!");
            }
        }
        
        private void Start()
        {
            // Subscribe to target loss events
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnAllLostTargetEvent>(OnAllTargetsLost);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnSwitchTargetEvent>(OnTargetSwitched);
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnAllLostTargetEvent>(OnAllTargetsLost);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnSwitchTargetEvent>(OnTargetSwitched);
        }
        
        private void Update()
        {
            // Check stabilization status
            if (isStabilizing && Time.time >= stabilizationEndTime)
            {
                EndStabilization();
            }
            
            // Check selection cooldown
            if (isInSelectionCooldown && Time.time >= selectionCooldownEndTime)
            {
                isInSelectionCooldown = false;
                LogDebug("Target selection cooldown ended");
            }
        }
        
        /// <summary>
        /// Handle single target lost - start stabilization
        /// </summary>
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            if (aimingModule != null && aimingModule.IsAiming)
            {
                StartStabilization("Single target lost");
            }
        }
        
        /// <summary>
        /// Handle all targets lost - start stabilization
        /// </summary>
        private void OnAllTargetsLost(OnAllLostTargetEvent eventData)
        {
            StartStabilization("All targets lost");
        }
        
        /// <summary>
        /// Handle target detection - check if we should allow it during cooldown
        /// </summary>
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            if (isInSelectionCooldown)
            {
                LogDebug("Target detection during cooldown - allowing but extending cooldown");
                // Extend cooldown slightly to prevent rapid switching
                selectionCooldownEndTime = Time.time + (targetSelectionCooldown * 0.5f);
            }
        }
        
        /// <summary>
        /// Handle target switching - start selection cooldown
        /// </summary>
        private void OnTargetSwitched(OnSwitchTargetEvent eventData)
        {
            StartSelectionCooldown("Target switched");
        }
        
        /// <summary>
        /// Start stabilization period after target loss
        /// </summary>
        private void StartStabilization(string reason)
        {
            if (!isStabilizing)
            {
                isStabilizing = true;
                stabilizationEndTime = Time.time + targetDeathStabilizationDelay;
                wasAimingBeforeStabilization = aimingModule != null && aimingModule.IsAiming;
                
                LogDebug($"Started targeting stabilization for {targetDeathStabilizationDelay}s - Reason: {reason}");
                
                // Temporarily disable aiming module updates to prevent rapid state changes
                if (aimingModule != null)
                {
                    aimingModule.CanUpdate = false;
                }
                
                // Start coroutine to handle stabilization
                StartCoroutine(StabilizationCoroutine());
            }
        }
        
        /// <summary>
        /// Start target selection cooldown to prevent rapid switching
        /// </summary>
        private void StartSelectionCooldown(string reason)
        {
            if (!isInSelectionCooldown)
            {
                isInSelectionCooldown = true;
                selectionCooldownEndTime = Time.time + targetSelectionCooldown;
                LogDebug($"Started target selection cooldown for {targetSelectionCooldown}s - Reason: {reason}");
            }
        }
        
        /// <summary>
        /// End stabilization and resume normal targeting
        /// </summary>
        private void EndStabilization()
        {
            isStabilizing = false;
            
            LogDebug("Targeting stabilization ended, resuming normal operation");
            
            // Re-enable aiming module updates
            if (aimingModule != null)
            {
                aimingModule.CanUpdate = true;
                
                // If we were aiming before, try to find a new target
                if (wasAimingBeforeStabilization)
                {
                    // Force a target search after stabilization
                    StartCoroutine(DelayedTargetSearch());
                }
            }
            
            // Start selection cooldown to prevent immediate rapid switching
            StartSelectionCooldown("Post-stabilization");
        }
        
        /// <summary>
        /// Coroutine to handle stabilization period
        /// </summary>
        private IEnumerator StabilizationCoroutine()
        {
            yield return new WaitForSeconds(targetDeathStabilizationDelay);
            
            // Stabilization will end in Update() when time is reached
            // This coroutine is just for additional logic if needed
        }
        
        /// <summary>
        /// Delayed target search after stabilization
        /// </summary>
        private IEnumerator DelayedTargetSearch()
        {
            yield return new WaitForSeconds(0.1f); // Small delay to ensure systems are stable
            
            if (aimingModule != null)
            {
                // Force update the aiming module to search for new targets
                LogDebug("Forcing target search after stabilization");
                
                // The aiming module will naturally search for targets in its next update
                // We just ensure it's enabled and ready
                if (!aimingModule.CanUpdate)
                {
                    aimingModule.CanUpdate = true;
                }
            }
        }
        
        /// <summary>
        /// Check if targeting is currently stabilizing
        /// </summary>
        public bool IsStabilizing => isStabilizing;
        
        /// <summary>
        /// Check if in target selection cooldown
        /// </summary>
        public bool IsInSelectionCooldown => isInSelectionCooldown;
        
        /// <summary>
        /// Force end stabilization (for external control)
        /// </summary>
        public void ForceEndStabilization()
        {
            if (isStabilizing)
            {
                LogDebug("Forced end of targeting stabilization");
                EndStabilization();
            }
        }
        
        /// <summary>
        /// Get stabilization status info
        /// </summary>
        public string GetStabilizationInfo()
        {
            if (isStabilizing)
            {
                float remainingTime = stabilizationEndTime - Time.time;
                return $"Stabilizing (remaining: {remainingTime:F1}s)";
            }
            else if (isInSelectionCooldown)
            {
                float remainingTime = selectionCooldownEndTime - Time.time;
                return $"Selection cooldown (remaining: {remainingTime:F1}s)";
            }
            else
            {
                return "Normal operation";
            }
        }
        
        /// <summary>
        /// Debug logging
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[TargetingStabilizer] {message}");
            }
        }
    }
}
