using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using Animation.Core;
using Module.Mono.Animancer.RealsticFemale;

namespace Animation.Debuging
{
    /// <summary>
    /// Comprehensive debugging and testing tool for the animation system
    /// </summary>
    public class AnimationSystemDebugger : MonoBehaviour
    {
        [Header("Debug Configuration")]
        [SerializeField] private bool enableDebugGUI = true;
        [SerializeField] private bool enableRealTimeLogging = false;
        [SerializeField] private bool showDetailedInfo = true;
        [SerializeField] private KeyCode debugToggleKey = KeyCode.F1;
        
        [Header("Component References")]
        [SerializeField] private UnifiedAnimationController unifiedController;
        [SerializeField] private EventDrivenAnimationManager eventManager;
        [SerializeField] private AnimationTransitionManager transitionManager;
        [SerializeField] private UnifiedLayeredAnimationAdapter layeredAdapter;
        
        private bool showDebugWindow = false;
        private Vector2 scrollPosition = Vector2.zero;
        private StringBuilder logBuilder = new StringBuilder();
        private List<string> recentLogs = new List<string>();
        private const int maxLogEntries = 50;
        
        private void Awake()
        {
            // Auto-find components if not assigned
            if (unifiedController == null)
                unifiedController = FindObjectOfType<UnifiedAnimationController>();
            if (eventManager == null)
                eventManager = FindObjectOfType<EventDrivenAnimationManager>();
            if (transitionManager == null)
                transitionManager = FindObjectOfType<AnimationTransitionManager>();
            if (layeredAdapter == null)
                layeredAdapter = FindObjectOfType<UnifiedLayeredAnimationAdapter>();
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(debugToggleKey))
            {
                showDebugWindow = !showDebugWindow;
            }
            
            if (enableRealTimeLogging)
            {
                LogCurrentState();
            }
        }
        
        private void OnGUI()
        {
            if (!enableDebugGUI || !showDebugWindow) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 600, Screen.height - 20));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Animation System Debugger", GUI.skin.GetStyle("label"));
            GUILayout.Label($"Press {debugToggleKey} to toggle this window", GUI.skin.GetStyle("label"));
            
            scrollPosition = GUILayout.BeginScrollView(scrollPosition);
            
            DrawSystemStatus();
            DrawAnimationState();
            DrawLayeredAnimationInfo();
            DrawEventStatistics();
            DrawTransitionInfo();
            DrawTestControls();
            DrawRecentLogs();
            
            GUILayout.EndScrollView();
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
        
        private void DrawSystemStatus()
        {
            GUILayout.Label("=== SYSTEM STATUS ===", GUI.skin.GetStyle("label"));
            
            if (unifiedController != null)
            {
                GUILayout.Label($"UnifiedAnimationController: {(unifiedController.IsInitialized ? "✅ Initialized" : "❌ Not Initialized")}");
                GUILayout.Label($"Animation System: {(unifiedController.UseAnimancer ? "Animancer" : "Animator Controller")}");
                GUILayout.Label($"Layered Animations: {(unifiedController.IsUpperBodyAnimationPlaying() ? "✅ Active" : "⭕ Inactive")}");
                GUILayout.Label($"Event-Driven Mode: {(unifiedController.IsEventDrivenModeActive() ? "✅ Active" : "⭕ Inactive")}");
                GUILayout.Label($"Advanced Transitions: {(unifiedController.IsAdvancedTransitionModeActive() ? "✅ Active" : "⭕ Inactive")}");
            }
            else
            {
                GUILayout.Label("❌ UnifiedAnimationController not found!");
            }
            
            GUILayout.Space(10);
        }
        
        private void DrawAnimationState()
        {
            GUILayout.Label("=== ANIMATION STATE ===", GUI.skin.GetStyle("label"));
            
            if (unifiedController != null && unifiedController.IsInitialized)
            {
                var context = unifiedController.CurrentContext;
                GUILayout.Label($"Main State: {context.mainState}");
                GUILayout.Label($"Movement: {context.movementState}");
                GUILayout.Label($"Weapon: {context.weaponState}");
                GUILayout.Label($"Weapon Module: {context.weaponModuleState}");
                GUILayout.Label($"Is Aiming: {context.isAiming}");
                GUILayout.Label($"Is Shooting: {context.isShooting}");
                GUILayout.Label($"Is Moving: {context.isMoving}");
                GUILayout.Label($"Input Magnitude: {context.inputMagnitude:F2}");
                
                if (showDetailedInfo)
                {
                    GUILayout.Label($"Upper Body Weight: {context.upperBodyWeight:F2}");
                    GUILayout.Label($"Lower Body Weight: {context.lowerBodyWeight:F2}");
                    GUILayout.Label($"State Change Time: {context.stateChangeTime:F2}");
                }
            }
            
            GUILayout.Space(10);
        }
        
        private void DrawLayeredAnimationInfo()
        {
            GUILayout.Label("=== LAYERED ANIMATION INFO ===", GUI.skin.GetStyle("label"));
            
            if (unifiedController != null)
            {
                GUILayout.Label($"Upper Body Playing: {unifiedController.IsUpperBodyAnimationPlaying()}");
                GUILayout.Label($"Upper Body Weight: {unifiedController.GetUpperBodyWeight():F2}");
            }
            
            if (layeredAdapter != null)
            {
                var currentAnim = layeredAdapter.GetCurrentUpperBodyAnimation();
                GUILayout.Label($"Current Upper Body Anim: {(currentAnim != null ? currentAnim.Name : "None")}");
                GUILayout.Label($"Adapter Weight: {layeredAdapter.GetUpperBodyWeight():F2}");
            }
            
            GUILayout.Space(10);
        }
        
        private void DrawEventStatistics()
        {
            GUILayout.Label("=== EVENT STATISTICS ===", GUI.skin.GetStyle("label"));
            
            if (unifiedController != null && unifiedController.IsEventDrivenModeActive())
            {
                var stats = unifiedController.GetEventStatistics();
                foreach (var stat in stats)
                {
                    GUILayout.Label($"{stat.Key}: {stat.Value}");
                }
            }
            else
            {
                GUILayout.Label("Event-driven mode not active");
            }
            
            GUILayout.Space(10);
        }
        
        private void DrawTransitionInfo()
        {
            GUILayout.Label("=== TRANSITION INFO ===", GUI.skin.GetStyle("label"));
            
            if (unifiedController != null && unifiedController.IsAdvancedTransitionModeActive())
            {
                GUILayout.Label($"Current Transition: {unifiedController.GetCurrentTransitionInfo()}");
                GUILayout.Label($"Queue Count: {unifiedController.GetTransitionQueueCount()}");
            }
            else
            {
                GUILayout.Label("Advanced transition mode not active");
            }
            
            GUILayout.Space(10);
        }
        
        private void DrawTestControls()
        {
            GUILayout.Label("=== TEST CONTROLS ===", GUI.skin.GetStyle("label"));
            
            if (GUILayout.Button("Test Idle Animation"))
            {
                TestIdleAnimation();
            }
            
            if (GUILayout.Button("Test Walking Animation"))
            {
                TestWalkingAnimation();
            }
            
            if (GUILayout.Button("Test Aiming Animation"))
            {
                TestAimingAnimation();
            }
            
            if (GUILayout.Button("Test Upper Body Fade Out"))
            {
                TestUpperBodyFadeOut();
            }
            
            if (GUILayout.Button("Force Animation Update"))
            {
                unifiedController?.ForceUpdate();
                AddLog("Forced animation update");
            }
            
            GUILayout.Space(10);
        }
        
        private void DrawRecentLogs()
        {
            GUILayout.Label("=== RECENT LOGS ===", GUI.skin.GetStyle("label"));
            
            foreach (var log in recentLogs)
            {
                GUILayout.Label(log, GUI.skin.GetStyle("label"));
            }
            
            if (GUILayout.Button("Clear Logs"))
            {
                recentLogs.Clear();
            }
        }
        
        private void LogCurrentState()
        {
            if (unifiedController != null && unifiedController.IsInitialized)
            {
                var info = unifiedController.GetCurrentAnimationInfo();
                if (!string.IsNullOrEmpty(info))
                {
                    AddLog($"[{Time.time:F1}] {info}");
                }
            }
        }
        
        private void AddLog(string message)
        {
            recentLogs.Add($"[{DateTime.Now:HH:mm:ss}] {message}");
            if (recentLogs.Count > maxLogEntries)
            {
                recentLogs.RemoveAt(0);
            }
            
            if (enableRealTimeLogging)
            {
                Debug.Log($"[AnimationSystemDebugger] {message}");
            }
        }
        
        // Test methods
        private void TestIdleAnimation()
        {
            if (eventManager != null)
            {
                eventManager.RequestMovementChange(MovementSubState.Standing);
                AddLog("Requested idle animation via EventManager");
            }
            else
            {
                AddLog("EventManager not available for idle test");
            }
        }
        
        private void TestWalkingAnimation()
        {
            if (eventManager != null)
            {
                eventManager.RequestMovementChange(MovementSubState.WalkingWithTurn, 1.0f, Vector3.forward, true);
                AddLog("Requested walking animation via EventManager");
            }
            else
            {
                AddLog("EventManager not available for walking test");
            }
        }
        
        private void TestAimingAnimation()
        {
            if (eventManager != null)
            {
                eventManager.RequestAnimationChange(MovementSubState.Standing, WeaponSubState.Aiming, 
                    WeaponSubModuleState.Pistol, true, false);
                AddLog("Requested aiming animation via EventManager");
            }
            else
            {
                AddLog("EventManager not available for aiming test");
            }
        }
        
        private void TestUpperBodyFadeOut()
        {
            if (unifiedController != null)
            {
                unifiedController.FadeOutUpperBody();
                AddLog("Requested upper body fade out");
            }
            else
            {
                AddLog("UnifiedController not available for fade out test");
            }
        }
    }
}
