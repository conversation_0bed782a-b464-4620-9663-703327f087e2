﻿using System;
using System.Collections.Generic;
using Animancer;
using Animancer.Examples.AnimatorControllers.GameKit;
using Module.Mono.Modules;
using UnityEngine;


namespace Module.Mono.Animancer.RealsticFemale
{
    public abstract class  CharacterState :State
    {
        [SerializeField] public AnimationModuleWithStates<CharacterState,MovementSubState> _Character;
        
        [SerializeField] public Dictionary<string,ClipTransition> ConditionalAnimations;
        public AnimationModuleWithStates<CharacterState,MovementSubState> Character => _Character;
        /************************************************************************************************************************/
    }
}