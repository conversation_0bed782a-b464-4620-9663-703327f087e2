using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Animation.Core;
using Module.Mono.Animancer.RealsticFemale;
using Events;

namespace Animation.Testing
{
    /// <summary>
    /// Automated testing suite for the animation system
    /// </summary>
    public class AnimationSystemTester : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runTestsOnStart = false;
        [SerializeField] private bool enableAutomatedTesting = false;
        [SerializeField] private float testInterval = 5f;
        [SerializeField] private bool logTestResults = true;
        
        [Header("Component References")]
        [SerializeField] private UnifiedAnimationController unifiedController;
        [SerializeField] private EventDrivenAnimationManager eventManager;
        [SerializeField] private AnimationTransitionManager transitionManager;
        [SerializeField] private UnifiedLayeredAnimationAdapter layeredAdapter;
        
        private List<TestResult> testResults = new List<TestResult>();
        private bool isTestingInProgress = false;
        
        [System.Serializable]
        public class TestResult
        {
            public string testName;
            public bool passed;
            public string details;
            public float executionTime;
            public System.DateTime timestamp;
            
            public override string ToString()
            {
                return $"[{timestamp:HH:mm:ss}] {testName}: {(passed ? "✅ PASS" : "❌ FAIL")} ({executionTime:F2}s) - {details}";
            }
        }
        
        private void Start()
        {
            // Auto-find components if not assigned
            if (unifiedController == null)
                unifiedController = FindObjectOfType<UnifiedAnimationController>();
            if (eventManager == null)
                eventManager = FindObjectOfType<EventDrivenAnimationManager>();
            if (transitionManager == null)
                transitionManager = FindObjectOfType<AnimationTransitionManager>();
            if (layeredAdapter == null)
                layeredAdapter = FindObjectOfType<UnifiedLayeredAnimationAdapter>();
            
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
            
            if (enableAutomatedTesting)
            {
                InvokeRepeating(nameof(RunRandomTest), testInterval, testInterval);
            }
        }
        
        #region Public Test Methods
        
        /// <summary>
        /// Run all animation system tests
        /// </summary>
        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            if (isTestingInProgress)
            {
                LogTest("Tests already in progress, skipping...");
                return;
            }
            
            StartCoroutine(RunAllTestsCoroutine());
        }
        
        /// <summary>
        /// Run a specific test by name
        /// </summary>
        public void RunSpecificTest(string testName)
        {
            switch (testName.ToLower())
            {
                case "initialization":
                    TestInitialization();
                    break;
                case "layered":
                    TestLayeredAnimations();
                    break;
                case "events":
                    TestEventDrivenSystem();
                    break;
                case "transitions":
                    TestAdvancedTransitions();
                    break;
                case "integration":
                    TestSystemIntegration();
                    break;
                default:
                    LogTest($"Unknown test: {testName}");
                    break;
            }
        }
        
        /// <summary>
        /// Get all test results
        /// </summary>
        public List<TestResult> GetTestResults()
        {
            return new List<TestResult>(testResults);
        }
        
        /// <summary>
        /// Clear all test results
        /// </summary>
        public void ClearTestResults()
        {
            testResults.Clear();
            LogTest("Test results cleared");
        }
        
        #endregion
        
        #region Test Coroutines
        
        private IEnumerator RunAllTestsCoroutine()
        {
            isTestingInProgress = true;
            LogTest("Starting comprehensive animation system tests...");
            
            yield return new WaitForSeconds(0.5f);
            
            // Test 1: Initialization
            TestInitialization();
            yield return new WaitForSeconds(1f);
            
            // Test 2: Layered Animations
            TestLayeredAnimations();
            yield return new WaitForSeconds(2f);
            
            // Test 3: Event-Driven System
            TestEventDrivenSystem();
            yield return new WaitForSeconds(2f);
            
            // Test 4: Advanced Transitions
            TestAdvancedTransitions();
            yield return new WaitForSeconds(2f);
            
            // Test 5: System Integration
            TestSystemIntegration();
            yield return new WaitForSeconds(1f);
            
            isTestingInProgress = false;
            LogTestSummary();
        }
        
        #endregion
        
        #region Individual Tests
        
        private void TestInitialization()
        {
            var startTime = Time.time;
            var testName = "System Initialization";
            
            try
            {
                bool allInitialized = true;
                var details = new List<string>();
                
                // Test UnifiedAnimationController
                if (unifiedController == null)
                {
                    allInitialized = false;
                    details.Add("UnifiedAnimationController not found");
                }
                else if (!unifiedController.IsInitialized)
                {
                    allInitialized = false;
                    details.Add("UnifiedAnimationController not initialized");
                }
                
                // Test LayeredAnimationAdapter
                if (layeredAdapter == null)
                {
                    details.Add("UnifiedLayeredAnimationAdapter not found (optional)");
                }
                
                // Test EventDrivenAnimationManager
                if (eventManager != null && !eventManager.IsInitialized)
                {
                    details.Add("EventDrivenAnimationManager not initialized (optional)");
                }
                
                var result = new TestResult
                {
                    testName = testName,
                    passed = allInitialized,
                    details = string.Join(", ", details),
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
            catch (System.Exception e)
            {
                var result = new TestResult
                {
                    testName = testName,
                    passed = false,
                    details = $"Exception: {e.Message}",
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
        }
        
        private void TestLayeredAnimations()
        {
            var startTime = Time.time;
            var testName = "Layered Animations";
            
            try
            {
                bool testPassed = true;
                var details = new List<string>();
                
                if (unifiedController == null)
                {
                    testPassed = false;
                    details.Add("UnifiedAnimationController not available");
                }
                else
                {
                    // Test upper body animation
                    var initialWeight = unifiedController.GetUpperBodyWeight();
                    
                    // Test fade out
                    unifiedController.FadeOutUpperBody(0.1f);
                    
                    // Wait a frame for the fade to start
                    StartCoroutine(DelayedWeightCheck(0.2f, (newWeight) =>
                    {
                        if (newWeight < initialWeight)
                        {
                            details.Add("Upper body fade out working");
                        }
                        else
                        {
                            testPassed = false;
                            details.Add("Upper body fade out not working");
                        }
                    }));
                    
                    details.Add($"Initial upper body weight: {initialWeight:F2}");
                }
                
                var result = new TestResult
                {
                    testName = testName,
                    passed = testPassed,
                    details = string.Join(", ", details),
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
            catch (System.Exception e)
            {
                var result = new TestResult
                {
                    testName = testName,
                    passed = false,
                    details = $"Exception: {e.Message}",
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
        }
        
        private void TestEventDrivenSystem()
        {
            var startTime = Time.time;
            var testName = "Event-Driven System";
            
            try
            {
                bool testPassed = true;
                var details = new List<string>();
                
                if (eventManager == null)
                {
                    details.Add("EventDrivenAnimationManager not available (optional)");
                }
                else if (!eventManager.IsInitialized)
                {
                    testPassed = false;
                    details.Add("EventDrivenAnimationManager not initialized");
                }
                else
                {
                    // Test event-driven animation request
                    var initialStats = eventManager.EventStatistics;
                    
                    eventManager.RequestMovementChange(MovementSubState.Standing);
                    
                    // Check if event was processed
                    var newStats = eventManager.EventStatistics;
                    if (newStats.Count > initialStats.Count || 
                        (newStats.ContainsKey("OnMovementStateChangedEvent") && 
                         newStats["OnMovementStateChangedEvent"] > (initialStats.ContainsKey("OnMovementStateChangedEvent") ? initialStats["OnMovementStateChangedEvent"] : 0)))
                    {
                        details.Add("Event processing working");
                    }
                    else
                    {
                        details.Add("Event processing may not be working");
                    }
                }
                
                var result = new TestResult
                {
                    testName = testName,
                    passed = testPassed,
                    details = string.Join(", ", details),
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
            catch (System.Exception e)
            {
                var result = new TestResult
                {
                    testName = testName,
                    passed = false,
                    details = $"Exception: {e.Message}",
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
        }
        
        private void TestAdvancedTransitions()
        {
            var startTime = Time.time;
            var testName = "Advanced Transitions";
            
            try
            {
                bool testPassed = true;
                var details = new List<string>();
                
                if (unifiedController == null)
                {
                    testPassed = false;
                    details.Add("UnifiedAnimationController not available");
                }
                else
                {
                    bool advancedMode = unifiedController.IsAdvancedTransitionModeActive();
                    details.Add($"Advanced transition mode: {(advancedMode ? "Active" : "Inactive")}");
                    
                    if (advancedMode)
                    {
                        var queueCount = unifiedController.GetTransitionQueueCount();
                        details.Add($"Transition queue count: {queueCount}");
                        
                        var transitionInfo = unifiedController.GetCurrentTransitionInfo();
                        details.Add($"Current transition: {transitionInfo}");
                    }
                }
                
                var result = new TestResult
                {
                    testName = testName,
                    passed = testPassed,
                    details = string.Join(", ", details),
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
            catch (System.Exception e)
            {
                var result = new TestResult
                {
                    testName = testName,
                    passed = false,
                    details = $"Exception: {e.Message}",
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
        }
        
        private void TestSystemIntegration()
        {
            var startTime = Time.time;
            var testName = "System Integration";
            
            try
            {
                bool testPassed = true;
                var details = new List<string>();
                
                // Test if all systems can work together
                if (unifiedController != null && unifiedController.IsInitialized)
                {
                    var context = unifiedController.CurrentContext;
                    details.Add($"Current state: {context.movementState}/{context.weaponState}");
                    
                    // Test context update
                    var newContext = context;
                    newContext.movementState = MovementSubState.Standing;
                    unifiedController.SetAnimationContext(newContext);
                    
                    details.Add("Context update test completed");
                }
                else
                {
                    testPassed = false;
                    details.Add("UnifiedAnimationController not available for integration test");
                }
                
                var result = new TestResult
                {
                    testName = testName,
                    passed = testPassed,
                    details = string.Join(", ", details),
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
            catch (System.Exception e)
            {
                var result = new TestResult
                {
                    testName = testName,
                    passed = false,
                    details = $"Exception: {e.Message}",
                    executionTime = Time.time - startTime,
                    timestamp = System.DateTime.Now
                };
                
                testResults.Add(result);
                LogTest(result.ToString());
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        private IEnumerator DelayedWeightCheck(float delay, System.Action<float> callback)
        {
            yield return new WaitForSeconds(delay);
            if (unifiedController != null)
            {
                callback(unifiedController.GetUpperBodyWeight());
            }
        }
        
        private void RunRandomTest()
        {
            if (isTestingInProgress) return;
            
            var tests = new string[] { "initialization", "layered", "events", "transitions", "integration" };
            var randomTest = tests[Random.Range(0, tests.Length)];
            RunSpecificTest(randomTest);
        }
        
        private void LogTest(string message)
        {
            if (logTestResults)
            {
                Debug.Log($"[AnimationSystemTester] {message}");
            }
        }
        
        private void LogTestSummary()
        {
            var passed = 0;
            var failed = 0;
            
            foreach (var result in testResults)
            {
                if (result.passed) passed++;
                else failed++;
            }
            
            LogTest($"Test Summary: {passed} passed, {failed} failed out of {testResults.Count} total tests");
        }
        
        #endregion
    }
}
