using System;
using System.Collections.Generic;
using UnityEngine;
using Animancer;
using Module.Mono.Animancer.RealsticFemale;
using Mono;
using Events;

namespace Animation.Core
{
    /// <summary>
    /// Unified Animation Controller that centralizes animation management
    /// and provides a single interface for both Animancer and Animator Controller systems
    /// </summary>
    [RequireComponent(typeof(HybridAnimancerComponent))]
    public class UnifiedAnimationController : MonoBehaviour
    {
        #region Inspector Fields

        [Header("Animation System Configuration")]
        [SerializeField] private bool useAnimancer = true;
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private float defaultTransitionDuration = 0.25f;

        [Header("Layered Animation Configuration")]
        [SerializeField] private AvatarMask upperBodyMask;
        [SerializeField] private float upperBodyFadeDuration = 0.25f;
        [SerializeField] private bool enableLayeredAnimations = true;

        [Header("Animation Modules")]
        [SerializeField] private CharacterAnimationModule characterAnimationModule;
        [SerializeField] private WeaponAnimationModule weaponAnimationModule;
        [SerializeField] private UnifiedLayeredAnimationAdapter layeredAnimationManager;

        [Header("State Management")]
        [SerializeField] private StateManager stateManager;
        [SerializeField] private CharacterParameters characterParameters;

        #endregion

        #region Private Fields

        private HybridAnimancerComponent animancerComponent;
        private AnimationStateContext currentContext;
        private AnimationStateContext previousContext;
        private bool isInitialized = false;
        private float lastUpdateTime;
        private readonly Dictionary<string, float> animationTimers = new Dictionary<string, float>();

        // Animation layer management
        private AnimancerLayer baseLayer;      // Lower body animations (movement)
        private AnimancerLayer actionLayer;    // Upper body animations (weapons/actions)
        private bool canPlayActionFullBody = false;

        #endregion

        #region Properties

        /// <summary>
        /// Current animation state context
        /// </summary>
        public AnimationStateContext CurrentContext => currentContext;

        /// <summary>
        /// Previous animation state context
        /// </summary>
        public AnimationStateContext PreviousContext => previousContext;

        /// <summary>
        /// Whether the controller is using Animancer or Animator Controller
        /// </summary>
        public bool UseAnimancer => useAnimancer;

        /// <summary>
        /// Whether the controller has been properly initialized
        /// </summary>
        public bool IsInitialized => isInitialized;

        /// <summary>
        /// Access to the underlying Animancer component
        /// </summary>
        public HybridAnimancerComponent AnimancerComponent => animancerComponent;

        #endregion

        #region Events

        /// <summary>
        /// Event fired when animation state context changes
        /// </summary>
        public event Action<AnimationStateContext, AnimationStateContext> OnAnimationStateChanged;

        /// <summary>
        /// Event fired when an animation transition starts
        /// </summary>
        public event Action<string, string, float> OnAnimationTransitionStarted;

        /// <summary>
        /// Event fired when an animation transition completes
        /// </summary>
        public event Action<string> OnAnimationTransitionCompleted;

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            Initialize();
        }

        private void Update()
        {
            if (!isInitialized) return;

            UpdateAnimationContext();
            ProcessAnimationUpdates();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the animation controller components
        /// </summary>
        private void InitializeComponents()
        {
            // Get required components
            animancerComponent = GetComponent<HybridAnimancerComponent>();
            if (animancerComponent == null)
            {
                Debug.LogError("[UnifiedAnimationController] HybridAnimancerComponent not found!");
                return;
            }

            // Find components if not assigned
            if (stateManager == null)
                stateManager = FindObjectOfType<StateManager>();

            if (characterParameters == null)
                characterParameters = FindObjectOfType<CharacterParameters>();

            if (characterAnimationModule == null)
                characterAnimationModule = GetComponent<CharacterAnimationModule>();

            if (weaponAnimationModule == null)
                weaponAnimationModule = GetComponent<WeaponAnimationModule>();

            if (layeredAnimationManager == null)
                layeredAnimationManager = GetComponent<UnifiedLayeredAnimationAdapter>();
        }

        /// <summary>
        /// Initialize the animation system
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
            {
                LogDebug("Already initialized, skipping...");
                return;
            }

            try
            {
                // Initialize animation modules
                InitializeAnimationModules();

                // Set up initial animation context
                currentContext = CreateInitialContext();
                previousContext = currentContext;

                // Configure Animancer/Animator Controller
                ConfigureAnimationSystem();

                isInitialized = true;
                LogDebug("UnifiedAnimationController initialized successfully");
            }
            catch (Exception e)
            {
                Debug.LogError($"[UnifiedAnimationController] Initialization failed: {e.Message}");
                isInitialized = false;
            }
        }

        /// <summary>
        /// Initialize the animation modules
        /// </summary>
        private void InitializeAnimationModules()
        {
            if (characterAnimationModule != null)
            {
                characterAnimationModule.Initialize(useAnimancer);
                LogDebug("Character animation module initialized");
            }

            if (weaponAnimationModule != null)
            {
                weaponAnimationModule.Initialize(useAnimancer);
                LogDebug("Weapon animation module initialized");
            }
        }

        /// <summary>
        /// Configure the animation system based on useAnimancer setting
        /// </summary>
        private void ConfigureAnimationSystem()
        {
            if (useAnimancer)
            {
                // Enable Animancer mode
                animancerComponent.enabled = true;

                // Initialize layered animation system if enabled
                if (enableLayeredAnimations)
                {
                    InitializeAnimationLayers();
                }

                LogDebug("Configured for Animancer mode");
            }
            else
            {
                // Configure for Animator Controller mode
                if (animancerComponent.Layers.Count > 0)
                {
                    animancerComponent.Layers[0].StartFade(0, defaultTransitionDuration);
                }
                LogDebug("Configured for Animator Controller mode");
            }
        }

        /// <summary>
        /// Initialize animation layers for layered animation support
        /// </summary>
        private void InitializeAnimationLayers()
        {
            if (animancerComponent == null) return;

            // Initialize base layer (lower body - movement animations)
            baseLayer = animancerComponent.Layers[0];
            baseLayer.SetDebugName("Base Layer (Lower Body)");

            // Initialize action layer (upper body - weapon/action animations)
            actionLayer = animancerComponent.Layers[1];
            actionLayer.SetDebugName("Action Layer (Upper Body)");

            // Set upper body mask if available
            if (upperBodyMask != null)
            {
                actionLayer.SetMask(upperBodyMask);
            }

            LogDebug("Animation layers initialized - Base Layer: Lower Body, Action Layer: Upper Body");
        }

        /// <summary>
        /// Create the initial animation context
        /// </summary>
        private AnimationStateContext CreateInitialContext()
        {
            if (stateManager != null && characterParameters != null)
            {
                return AnimationStateContextUtility.CreateFromCurrentState(stateManager, characterParameters);
            }

            LogDebug("Using default animation context (StateManager or CharacterParameters not found)");
            return AnimationStateContext.Default;
        }

        #endregion

        #region Animation Context Management

        /// <summary>
        /// Update the current animation context from game state
        /// </summary>
        private void UpdateAnimationContext()
        {
            if (stateManager == null || characterParameters == null) return;

            var newContext = AnimationStateContextUtility.CreateFromCurrentState(stateManager, characterParameters);

            if (newContext.HasChanged(currentContext))
            {
                previousContext = currentContext;
                currentContext = newContext;

                OnAnimationStateChanged?.Invoke(previousContext, currentContext);

                // Broadcast events using your EventManager
                BroadcastStateChangeEvents(previousContext, currentContext);

                LogDebug($"Animation context changed: {AnimationStateContextUtility.CompareContexts(previousContext, currentContext)}");
            }
        }

        /// <summary>
        /// Manually set the animation context (useful for testing)
        /// </summary>
        public void SetAnimationContext(AnimationStateContext context)
        {
            if (!AnimationStateContextUtility.IsValidContext(context, out string errorMessage))
            {
                Debug.LogWarning($"[UnifiedAnimationController] Invalid context: {errorMessage}");
                return;
            }

            previousContext = currentContext;
            currentContext = context;

            OnAnimationStateChanged?.Invoke(previousContext, currentContext);
        }

        #endregion

        #region Animation Updates

        /// <summary>
        /// Process animation updates based on current context
        /// </summary>
        private void ProcessAnimationUpdates()
        {
            if (!currentContext.HasChanged(previousContext)) return;

            // Validate transition
            if (!AnimationStateContextUtility.IsValidTransition(previousContext, currentContext, out string reason))
            {
                Debug.LogWarning($"[UnifiedAnimationController] Invalid transition: {reason}");
                return;
            }

            // Get transition priority and duration
            var priority = AnimationStateContextUtility.GetTransitionPriority(previousContext, currentContext);
            var duration = AnimationStateContextUtility.GetRecommendedTransitionDuration(previousContext, currentContext);

            // Process different types of changes
            if (currentContext.HasMovementChanged(previousContext))
            {
                ProcessMovementTransition(duration);
            }

            if (currentContext.HasWeaponChanged(previousContext))
            {
                ProcessWeaponTransition(duration);
            }

            LogDebug($"Processed animation transition with priority {priority} and duration {duration:F2}s");
        }

        /// <summary>
        /// Process movement-related animation transitions
        /// </summary>
        private void ProcessMovementTransition(float duration)
        {
            if (characterAnimationModule == null) return;

            try
            {
                if (useAnimancer)
                {
                    characterAnimationModule.PlayAnimationState(currentContext.movementState, duration);
                }
                else
                {
                    // Update Animator Controller parameters
                    characterAnimationModule.SetFloat("InputMagnitude", currentContext.inputMagnitude);
                    characterAnimationModule.SetFloat("InputAngle", currentContext.inputAngle);
                    characterAnimationModule.SetBool("IsMoving", currentContext.isMoving);
                    characterAnimationModule.SetBool("IsStopping", currentContext.isStopping);
                }

                OnAnimationTransitionStarted?.Invoke(
                    previousContext.movementState.ToString(),
                    currentContext.movementState.ToString(),
                    duration);

                // Broadcast transition started event
                var transitionEvent = new OnAnimationTransitionStartedEvent(
                    previousContext.movementState.ToString(),
                    currentContext.movementState.ToString(),
                    duration, false, true);
                EventManager.Broadcast(transitionEvent);
            }
            catch (Exception e)
            {
                Debug.LogError($"[UnifiedAnimationController] Movement transition failed: {e.Message}");
            }
        }

        /// <summary>
        /// Process weapon-related animation transitions
        /// </summary>
        private void ProcessWeaponTransition(float duration)
        {
            if (weaponAnimationModule == null) return;

            try
            {
                if (useAnimancer)
                {
                    weaponAnimationModule.PlayAnimationState(currentContext.weaponState, duration);
                }
                else
                {
                    // Update Animator Controller parameters
                    weaponAnimationModule.SetBool("IsAiming", currentContext.isAiming);
                    weaponAnimationModule.SetBool("IsShooting", currentContext.isShooting);
                    weaponAnimationModule.SetTrigger(currentContext.weaponState.ToString());
                }

                OnAnimationTransitionStarted?.Invoke(
                    previousContext.weaponState.ToString(),
                    currentContext.weaponState.ToString(),
                    duration);

                // Broadcast transition started event
                var transitionEvent = new OnAnimationTransitionStartedEvent(
                    previousContext.weaponState.ToString(),
                    currentContext.weaponState.ToString(),
                    duration, true, false);
                EventManager.Broadcast(transitionEvent);
            }
            catch (Exception e)
            {
                Debug.LogError($"[UnifiedAnimationController] Weapon transition failed: {e.Message}");
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Switch between Animancer and Animator Controller at runtime
        /// </summary>
        public void SetAnimationSystem(bool useAnimancerSystem)
        {
            if (useAnimancer == useAnimancerSystem) return;

            useAnimancer = useAnimancerSystem;
            ConfigureAnimationSystem();
            InitializeAnimationModules();

            LogDebug($"Switched to {(useAnimancer ? "Animancer" : "Animator Controller")} system");
        }

        /// <summary>
        /// Get current animation state information
        /// </summary>
        public string GetCurrentAnimationInfo()
        {
            return currentContext.ToDetailedString();
        }

        /// <summary>
        /// Force an immediate animation update
        /// </summary>
        public void ForceUpdate()
        {
            UpdateAnimationContext();
            ProcessAnimationUpdates();
        }

        #endregion

        #region Layered Animation API

        /// <summary>
        /// Play lower body animation (movement animations)
        /// </summary>
        public void PlayLowerBodyAnimation(ITransition transition, float fadeDuration = -1f)
        {
            if (!enableLayeredAnimations || baseLayer == null)
            {
                LogDebug("Layered animations not enabled or base layer not initialized");
                return;
            }

            float duration = fadeDuration > 0 ? fadeDuration : defaultTransitionDuration;

            canPlayActionFullBody = transition != null;

            if (canPlayActionFullBody && actionLayer != null && actionLayer.TargetWeight > 0)
            {
                PlayActionFullBody(duration);
            }
            else
            {
                baseLayer.Play(transition, duration);
            }

            LogDebug($"Playing lower body animation: {transition?.GetType().Name}");
        }

        /// <summary>
        /// Play upper body animation (weapon/action animations)
        /// </summary>
        public void PlayUpperBodyAnimation(ITransition transition, float fadeDuration = -1f)
        {
            if (!enableLayeredAnimations || actionLayer == null)
            {
                LogDebug("Layered animations not enabled or action layer not initialized");
                return;
            }

            float duration = fadeDuration > 0 ? fadeDuration : upperBodyFadeDuration;
            actionLayer.Play(transition, duration);

            if (canPlayActionFullBody)
            {
                PlayActionFullBody(duration);
            }

            LogDebug($"Playing upper body animation: {transition?.GetType().Name}");
        }

        /// <summary>
        /// Fade out upper body animation
        /// </summary>
        public void FadeOutUpperBody(float fadeDuration = -1f)
        {
            if (!enableLayeredAnimations || actionLayer == null) return;

            float duration = fadeDuration > 0 ? fadeDuration : upperBodyFadeDuration;
            actionLayer.StartFade(0, duration);

            LogDebug("Fading out upper body animation");
        }

        /// <summary>
        /// Play action animation on full body when appropriate
        /// </summary>
        private void PlayActionFullBody(float fadeDuration)
        {
            if (actionLayer?.CurrentState == null || baseLayer == null) return;

            var actionState = actionLayer.CurrentState;
            var baseState = baseLayer.Play(actionState.Clip, fadeDuration);
            baseState.NormalizedTime = actionState.NormalizedTime;

            LogDebug("Playing action animation on full body");
        }

        /// <summary>
        /// Check if upper body animation is currently playing
        /// </summary>
        public bool IsUpperBodyAnimationPlaying()
        {
            return enableLayeredAnimations && actionLayer != null && actionLayer.TargetWeight > 0;
        }

        /// <summary>
        /// Get current upper body animation weight
        /// </summary>
        public float GetUpperBodyWeight()
        {
            return enableLayeredAnimations && actionLayer != null ? actionLayer.TargetWeight : 0f;
        }

        #endregion

        #region Event Broadcasting

        /// <summary>
        /// Broadcast state change events using your EventManager
        /// </summary>
        private void BroadcastStateChangeEvents(AnimationStateContext previous, AnimationStateContext current)
        {
            // Broadcast movement state change if needed
            if (current.HasMovementChanged(previous))
            {
                var movementEvent = new OnMovementStateChangedEvent(
                    previous.movementState, current.movementState,
                    current.inputMagnitude, current.inputDirection, current.isMoving);
                EventManager.Broadcast(movementEvent);
            }

            // Broadcast weapon state change if needed
            if (current.HasWeaponChanged(previous))
            {
                var weaponEvent = new OnWeaponStateChangedEvent(
                    previous.weaponState, current.weaponState,
                    current.weaponModuleState, current.isAiming, current.isShooting);
                EventManager.Broadcast(weaponEvent);
            }

            // Broadcast combat mode change if needed
            if (previous.mainState != current.mainState)
            {
                var combatEvent = new OnCombatModeChangedEvent(
                    current.mainState == MainState.Combat, current.mainState,
                    current.aimTarget, current.detectTarget);
                EventManager.Broadcast(combatEvent);
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Log debug message if debug logging is enabled
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[UnifiedAnimationController] {message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// Configuration ScriptableObject for UnifiedAnimationController
    /// </summary>
    [CreateAssetMenu(fileName = "UnifiedAnimationConfig", menuName = "Animation/Unified Animation Config")]
    public class UnifiedAnimationConfig : ScriptableObject
    {
        [Header("System Settings")]
        public bool useAnimancerByDefault = true;
        public bool enableDebugLogging = false;
        public float defaultTransitionDuration = 0.25f;
        public float updateRate = 30f; // FPS for animation updates

        [Header("Transition Durations")]
        public float criticalTransitionDuration = 0.1f;
        public float highPriorityTransitionDuration = 0.15f;
        public float mediumPriorityTransitionDuration = 0.25f;
        public float lowPriorityTransitionDuration = 0.35f;

        [Header("Performance Settings")]
        public bool enableAnimationLOD = true;
        public float lodDistance = 50f;
        public bool batchAnimationUpdates = true;
        public int maxAnimationsPerFrame = 10;

        [Header("Debug Settings")]
        public bool showAnimationGizmos = false;
        public bool logStateTransitions = false;
        public bool validateTransitions = true;

        /// <summary>
        /// Get transition duration based on priority
        /// </summary>
        public float GetTransitionDuration(TransitionPriority priority)
        {
            return priority switch
            {
                TransitionPriority.Critical => criticalTransitionDuration,
                TransitionPriority.High => highPriorityTransitionDuration,
                TransitionPriority.Medium => mediumPriorityTransitionDuration,
                TransitionPriority.Low => lowPriorityTransitionDuration,
                _ => defaultTransitionDuration
            };
        }
    }
}
