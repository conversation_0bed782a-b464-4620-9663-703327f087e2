using System;
using System.Collections.Generic;
using Animancer;
using DefaultNamespace.Mono.Interface;
using Module.Mono.Modules;
using Sirenix.Utilities;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public class WeaponAnimationModule:AnimationModuleWithStates<WeaponState,WeaponSubModuleState>
        ,INeedState<WeaponSubState>,IUpdateState<WeaponSubModuleState>,INeedInput<CharacterParameter>
    {
        [SerializeField] private UnifiedLayeredAnimationAdapter unifiedLayeredAnimationAdapter;
        [SerializeField] private WeaponSubState _subModuleState;
        [SerializeField] private bool m_autoAiming;
        [SerializeField] private bool m_detectTarget;
        
        private WeaponState m_lastState;
        
        public void SetUpperBodyAnimation(ClipTransition animation)
        {
            unifiedLayeredAnimationAdapter.PlayUpperBodyAnimation(animation);
        }

        [field:SerializeField] public List<CharacterParameter> InputName { get; private set; }
        public void HandleInput(Dictionary<CharacterParameter, object> inputValue)
        {
            if (inputValue.TryGetValue(CharacterParameter.AutoAiming, out var autoAimingValue))
            {
                m_autoAiming = (bool)autoAimingValue;
            }
            
            if (inputValue.TryGetValue(CharacterParameter.DetectTarget, out var detectTargetValue))
            {
                m_detectTarget = (bool)detectTargetValue;
            }
        }
        
        [field:SerializeField] public WeaponSubState State { get; private set; }
        public void OnStateChange(WeaponSubState newState)
        {
            State = newState;
            
            SubState = (WeaponSubModuleState)PlayerController.Instance.StateManager.GetState(typeof(WeaponSubModuleState));

            if (SubState == WeaponSubModuleState.EmptyHand)
            {
                States.Keys.ForEach(x =>
                {
                    States[x].OnExitState();      
                });
                unifiedLayeredAnimationAdapter.FadeOutUpperBody();
                return;
            }

            switch (newState)
            {
                case WeaponSubState.Equipping:
                    if (!m_detectTarget)
                    {
                        _subModuleState = WeaponSubState.Idle;
                        SetState();
                    }
                    else
                    {
                        _subModuleState = WeaponSubState.Aiming;
                        SetState();
                    }
                    States[SubState].OnEnterState(0.25f, newState, () => { });

                    States[SubState].gameObject.SetActive(true);
                    break;
                case WeaponSubState.UnEquipping:
                    m_lastState.OnExitState();
                    m_lastState.gameObject.SetActive(false);
                    break;
                case WeaponSubState.Idle:
                    States[SubState].OnEnterState(0.25f, newState,null);
                    break;
                case WeaponSubState.Aiming:
                    States[SubState].OnEnterState(0.25f, newState,null);
                    break;
                case WeaponSubState.Reloading:
                    States[SubState].OnEnterState(0.25f, newState,null);
                    break;
                case WeaponSubState.Shooting:
                    // Play shooting animation with a fast transition time
                    States[SubState].OnEnterState(0.1f, newState, null);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(newState), newState, null);
            }
            
            m_lastState = States[SubState];
        }

        public Type GetModuleState()
        {
            return State.GetType();
        }
        
        public void SetState()
        {
            PlayerController.Instance.StateManager.CurrentSubState = _subModuleState;
        }
    }
}