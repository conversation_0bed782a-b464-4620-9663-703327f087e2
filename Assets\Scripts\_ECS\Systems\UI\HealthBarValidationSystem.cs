using Unity.Entities;
using Unity.Transforms;
using UnityEngine;
using PlayerFAP.Components;
using PlayerFAP.Mono.UI.Health;

namespace PlayerFAP.Systems.UI
{
    /// <summary>
    /// System that validates health bars to ensure they only appear on entities with detection tags
    /// This is a safety net to prevent health bars from showing on entities without detection
    /// </summary>
    [UpdateAfter(typeof(HealthBarCleanupSystem))]
    public partial class HealthBarValidationSystem : SystemBase
    {
        private EntityQuery _invalidHealthBarQuery;

        protected override void OnCreate()
        {
            // Query for entities that have health bars but no detection tag and no hide timer
            _invalidHealthBarQuery = GetEntityQuery(
                ComponentType.ReadOnly<HealthComponent>(),
                ComponentType.ReadOnly<HealthBarUIReference>(),
                ComponentType.Exclude<DetectedTag>(),
                ComponentType.Exclude<AlwaysShowHealthBarTag>(),
                ComponentType.Exclude<HealthBarHideTimerComponent>(),
                ComponentType.Exclude<DeadTag>()
            );
        }

        protected override void OnUpdate()
        {
            if (HealthBarUIManager.Instance == null) return;

            var healthBarSpawnSystem = World.GetExistingSystemManaged<HealthBarSpawnSystem>();

            int invalidCount = _invalidHealthBarQuery.CalculateEntityCount();
            if (invalidCount > 0)
            {
                //Debug.LogWarning($"HealthBarValidationSystem: Found {invalidCount} entities with invalid health bars (no detection tag)");
            }

            Entities
                .WithAll<HealthComponent, HealthBarUIReference>()
                .WithNone<DetectedTag>()
                .WithNone<AlwaysShowHealthBarTag>()
                .ForEach((Entity entity, ref HealthBarUIReference uiRef) =>
                {
                    if (uiRef.UIEntity != Entity.Null && EntityManager.Exists(uiRef.UIEntity))
                    {
                        if (EntityManager.HasComponent<CompanionLink>(uiRef.UIEntity))
                        {
                            var companionLink = EntityManager.GetComponentData<CompanionLink>(uiRef.UIEntity);
                            var uiObject = companionLink.Companion.Value;
                            if (uiObject != null)
                            {
                                HealthBarUIManager.Instance.ReturnUIToPool(uiObject);
                                #if DEBUG_LOG_MANAGER
                                //DebugLogManager.Instance.Log($"[HealthBarValidationSystem] Returned UI object to pool for entity {entity.Index}");
                                #else
                                //UnityEngine.Debug.Log($"[HealthBarValidationSystem] Returned UI object to pool for entity {entity.Index}");
                                #endif
                            }
                        }
                        healthBarSpawnSystem.ReturnUIEntityToPool(uiRef.UIEntity);
                        #if DEBUG_LOG_MANAGER
                        //DebugLogManager.Instance.Log($"[HealthBarValidationSystem] Returned UI entity {uiRef.UIEntity.Index} to pool");
                        #else
                        //UnityEngine.Debug.Log($"[HealthBarValidationSystem] Returned UI entity {uiRef.UIEntity.Index} to pool");
                        #endif
                    }

                    // Remove the reference component
                    EntityManager.RemoveComponent<HealthBarUIReference>(entity);
                    
                    // Also remove HealthBarLink if it exists
                    if (EntityManager.HasComponent<HealthBarLink>(entity))
                    {
                        EntityManager.RemoveComponent<HealthBarLink>(entity);
                    }
                })
                .WithoutBurst()
                .WithStructuralChanges()
                .Run();
        }
    }
}
