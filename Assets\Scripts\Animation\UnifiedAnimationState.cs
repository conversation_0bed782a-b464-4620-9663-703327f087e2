using System;
using UnityEngine;
using Module.Mono.Animancer.RealsticFemale;

namespace Animation.StateSync
{
    /// <summary>
    /// Unified animation state that consolidates all animation-related states
    /// into a single, coherent structure for centralized state management.
    /// </summary>
    [Serializable]
    public struct UnifiedAnimationState : IEquatable<UnifiedAnimationState>
    {
        #region Core States
        
        [Header("Core States")]
        public MainState mainState;
        public MovementSubState movementState;
        public WeaponSubState weaponState;
        public AnimationSubState animationState;
        public WeaponSubModuleState weaponModuleState;
        
        #endregion
        
        #region Metadata
        
        [Header("State Metadata")]
        public float timestamp;
        public int frameCount;
        public bool isValid;
        
        #endregion
        
        #region Constructors
        
        /// <summary>
        /// Create a unified state with all parameters
        /// </summary>
        public UnifiedAnimationState(MainState main, MovementSubState movement, WeaponSubState weapon, 
            AnimationSubState animation, WeaponSubModuleState weaponModule)
        {
            mainState = main;
            movementState = movement;
            weaponState = weapon;
            animationState = animation;
            weaponModuleState = weaponModule;
            timestamp = Time.time;
            frameCount = Time.frameCount;
            isValid = true;
        }
        
        /// <summary>
        /// Create a default unified state
        /// </summary>
        public static UnifiedAnimationState Default => new UnifiedAnimationState
        {
            mainState = MainState.Normal,
            movementState = MovementSubState.Standing,
            weaponState = WeaponSubState.Idle,
            animationState = AnimationSubState.Idle,
            weaponModuleState = WeaponSubModuleState.EmptyHand,
            timestamp = Time.time,
            frameCount = Time.frameCount,
            isValid = true
        };
        
        #endregion
        
        #region Equality and Comparison
        
        /// <summary>
        /// Check if this state equals another state
        /// </summary>
        public bool Equals(UnifiedAnimationState other)
        {
            return mainState == other.mainState &&
                   movementState == other.movementState &&
                   weaponState == other.weaponState &&
                   animationState == other.animationState &&
                   weaponModuleState == other.weaponModuleState;
        }
        
        /// <summary>
        /// Override equals for object comparison
        /// </summary>
        public override bool Equals(object obj)
        {
            return obj is UnifiedAnimationState other && Equals(other);
        }
        
        /// <summary>
        /// Get hash code for this state
        /// </summary>
        public override int GetHashCode()
        {
            return HashCode.Combine(
                (int)mainState,
                (int)movementState,
                (int)weaponState,
                (int)animationState,
                (int)weaponModuleState
            );
        }
        
        /// <summary>
        /// Equality operator
        /// </summary>
        public static bool operator ==(UnifiedAnimationState left, UnifiedAnimationState right)
        {
            return left.Equals(right);
        }
        
        /// <summary>
        /// Inequality operator
        /// </summary>
        public static bool operator !=(UnifiedAnimationState left, UnifiedAnimationState right)
        {
            return !left.Equals(right);
        }
        
        #endregion
        
        #region State Analysis
        
        /// <summary>
        /// Check if movement state has changed from another state
        /// </summary>
        public bool HasMovementChanged(UnifiedAnimationState other)
        {
            return movementState != other.movementState;
        }
        
        /// <summary>
        /// Check if weapon state has changed from another state
        /// </summary>
        public bool HasWeaponChanged(UnifiedAnimationState other)
        {
            return weaponState != other.weaponState || weaponModuleState != other.weaponModuleState;
        }
        
        /// <summary>
        /// Check if animation state has changed from another state
        /// </summary>
        public bool HasAnimationChanged(UnifiedAnimationState other)
        {
            return animationState != other.animationState;
        }
        
        /// <summary>
        /// Check if main state has changed from another state
        /// </summary>
        public bool HasMainStateChanged(UnifiedAnimationState other)
        {
            return mainState != other.mainState;
        }
        
        /// <summary>
        /// Check if any state has changed from another state
        /// </summary>
        public bool HasAnyStateChanged(UnifiedAnimationState other)
        {
            return HasMainStateChanged(other) || HasMovementChanged(other) || 
                   HasWeaponChanged(other) || HasAnimationChanged(other);
        }
        
        /// <summary>
        /// Get the number of states that have changed
        /// </summary>
        public int GetChangeCount(UnifiedAnimationState other)
        {
            int changes = 0;
            if (HasMainStateChanged(other)) changes++;
            if (HasMovementChanged(other)) changes++;
            if (HasWeaponChanged(other)) changes++;
            if (HasAnimationChanged(other)) changes++;
            return changes;
        }
        
        #endregion
        
        #region State Validation
        
        /// <summary>
        /// Check if this state is valid
        /// </summary>
        public bool IsValidState()
        {
            // Basic validation rules
            if (!isValid) return false;
            
            // Check for invalid state combinations
            if (mainState == MainState.Combat && weaponModuleState == WeaponSubModuleState.EmptyHand)
            {
                // Combat mode with empty hands might be invalid depending on game rules
                // This is a design decision
            }
            
            if (weaponState == WeaponSubState.Shooting && weaponModuleState == WeaponSubModuleState.EmptyHand)
            {
                return false; // Can't shoot with empty hands
            }
            
            if (animationState == AnimationSubState.Shooting && weaponState != WeaponSubState.Shooting)
            {
                return false; // Animation and weapon state mismatch
            }
            
            return true;
        }
        
        /// <summary>
        /// Get validation errors for this state
        /// </summary>
        public string GetValidationErrors()
        {
            var errors = new System.Collections.Generic.List<string>();
            
            if (!isValid)
                errors.Add("State marked as invalid");
                
            if (weaponState == WeaponSubState.Shooting && weaponModuleState == WeaponSubModuleState.EmptyHand)
                errors.Add("Cannot shoot with empty hands");
                
            if (animationState == AnimationSubState.Shooting && weaponState != WeaponSubState.Shooting)
                errors.Add("Animation and weapon state mismatch for shooting");
                
            return string.Join("; ", errors);
        }
        
        #endregion
        
        #region String Representation
        
        /// <summary>
        /// Get a detailed string representation of this state
        /// </summary>
        public override string ToString()
        {
            return $"UnifiedState[Main:{mainState}, Movement:{movementState}, Weapon:{weaponState}, " +
                   $"Animation:{animationState}, WeaponModule:{weaponModuleState}, Frame:{frameCount}]";
        }
        
        /// <summary>
        /// Get a compact string representation
        /// </summary>
        public string ToCompactString()
        {
            return $"{mainState}|{movementState}|{weaponState}|{animationState}|{weaponModuleState}";
        }
        
        /// <summary>
        /// Get a detailed string with timing information
        /// </summary>
        public string ToDetailedString()
        {
            return $"UnifiedAnimationState:\n" +
                   $"  Main State: {mainState}\n" +
                   $"  Movement: {movementState}\n" +
                   $"  Weapon: {weaponState}\n" +
                   $"  Animation: {animationState}\n" +
                   $"  Weapon Module: {weaponModuleState}\n" +
                   $"  Timestamp: {timestamp:F3}s\n" +
                   $"  Frame: {frameCount}\n" +
                   $"  Valid: {isValid}";
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Create a copy of this state with updated timestamp
        /// </summary>
        public UnifiedAnimationState WithUpdatedTimestamp()
        {
            var copy = this;
            copy.timestamp = Time.time;
            copy.frameCount = Time.frameCount;
            return copy;
        }
        
        /// <summary>
        /// Create a copy of this state with a specific main state
        /// </summary>
        public UnifiedAnimationState WithMainState(MainState newMainState)
        {
            var copy = this;
            copy.mainState = newMainState;
            copy.timestamp = Time.time;
            copy.frameCount = Time.frameCount;
            return copy;
        }
        
        /// <summary>
        /// Create a copy of this state with a specific movement state
        /// </summary>
        public UnifiedAnimationState WithMovementState(MovementSubState newMovementState)
        {
            var copy = this;
            copy.movementState = newMovementState;
            copy.timestamp = Time.time;
            copy.frameCount = Time.frameCount;
            return copy;
        }
        
        /// <summary>
        /// Create a copy of this state with a specific weapon state
        /// </summary>
        public UnifiedAnimationState WithWeaponState(WeaponSubState newWeaponState)
        {
            var copy = this;
            copy.weaponState = newWeaponState;
            copy.timestamp = Time.time;
            copy.frameCount = Time.frameCount;
            return copy;
        }
        
        /// <summary>
        /// Create a copy of this state with a specific animation state
        /// </summary>
        public UnifiedAnimationState WithAnimationState(AnimationSubState newAnimationState)
        {
            var copy = this;
            copy.animationState = newAnimationState;
            copy.timestamp = Time.time;
            copy.frameCount = Time.frameCount;
            return copy;
        }
        
        #endregion
    }
}
