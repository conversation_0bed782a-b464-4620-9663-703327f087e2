%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 07228ccb777150f48a744eab7c717139, type: 3}
  m_Name: PistolConfig
  m_EditorClassIdentifier: 
  weaponName: Pistol
  weaponType: 0
  defaultBulletType: 0
  baseMaxAmmo: 12
  baseReloadTime: 1.5
  baseStats:
    fireRate: 0.1
    accuracy: 0.85
    maxRange: 50
    damageAmount: 15
    spreadAngle: 3
    recoilAmount: 0.8
    recoilRecovery: 1.5
  detectionRadius: 9
  detectionRange: 9
  detectionAngle: 60
  coneAngle: 60
  coneRange: 7
  upgradeLevels:
  - levelName: Enhanced Pistol
    upgradeCost: 100
    stats:
      fireRate: 15
      accuracy: 0.88
      maxRange: 55
      damageAmount: 18
      spreadAngle: 5
      recoilAmount: 0.7
      recoilRecovery: 1.7
    additionalAmmo: 3
    reloadTimeReduction: 0.1
    additionalDetectionRadius: 0
    additionalDetectionRange: 0
    additionalDetectionAngle: 0
    additionalConeAngle: 0
    additionalConeRange: 0
  - levelName: Advanced Pistol
    upgradeCost: 250
    stats:
      fireRate: 7
      accuracy: 0.91
      maxRange: 60
      damageAmount: 22
      spreadAngle: 2.5
      recoilAmount: 0.6
      recoilRecovery: 2
    additionalAmmo: 5
    reloadTimeReduction: 0.15
    additionalDetectionRadius: 0
    additionalDetectionRange: 0
    additionalDetectionAngle: 0
    additionalConeAngle: 0
    additionalConeRange: 0
  - levelName: Elite Pistol
    upgradeCost: 500
    stats:
      fireRate: 8
      accuracy: 0.95
      maxRange: 70
      damageAmount: 28
      spreadAngle: 2
      recoilAmount: 0.5
      recoilRecovery: 2.5
    additionalAmmo: 8
    reloadTimeReduction: 0.2
    additionalDetectionRadius: 0
    additionalDetectionRange: 0
    additionalDetectionAngle: 0
    additionalConeAngle: 0
    additionalConeRange: 0
