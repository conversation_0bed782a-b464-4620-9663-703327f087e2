%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 1
  m_FogColor: {r: 0.121568635, g: 0.1254902, b: 0.121568635, a: 0.019607844}
  m_FogMode: 1
  m_FogDensity: 0.01
  m_LinearFogStart: 25
  m_LinearFogEnd: 100
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 0
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 0
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: 290b61996be0de24a9d0d112c6a452b2,
    type: 2}
  m_LightingSettings: {fileID: 488497883}
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!114 &5918553 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7142898169479841738, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 583b0b8f3bcc4bf6b0e1be30c2aa1e2c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &19836680 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400028, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &52640610 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0dcbffa32d899b4cac41b83a61cef7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &92037584 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaf09bfab896407fa91b5c272c57cb0d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &98107572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 98107573}
  - component: {fileID: 98107576}
  - component: {fileID: 98107575}
  - component: {fileID: 98107574}
  m_Layer: 9
  m_Name: Bip002 R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &98107573
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 98107572}
  serializedVersion: 2
  m_LocalRotation: {x: 0.33834022, y: 0.16449948, z: -0.7288645, w: -0.57203364}
  m_LocalPosition: {x: 0.4202161, y: 0.99223554, z: -0.045661904}
  m_LocalScale: {x: 1, y: 0.9999998, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -8.4698, y: -43.5447, z: 107.1365}
--- !u!153 &98107574
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 98107572}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 697665426}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -1, z: -0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.24303263, y: 0.00000007450579, z: -0.000000081956394}
  m_SecondaryAxis: {x: 0, y: 0, z: -1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &98107575
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 98107572}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.06835291
  m_Height: 0.20050187
  m_Direction: 0
  m_Center: {x: -0.091137275, y: 0.000000061794154, z: -0.000000090762306}
--- !u!54 &98107576
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 98107572}
  serializedVersion: 5
  m_Mass: 0.49500003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &124257273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 124257274}
  - component: {fileID: 124257275}
  m_Layer: 0
  m_Name: Behaviours
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &124257274
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 124257273}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2027615250}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &124257275
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 124257273}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: All Puppet Behaviours should be parented to this GameObject, the PuppetMaster
    will automatically find them from here. All Puppet Behaviours have been designed
    so that they could be simply copied from one character to another without changing
    any references. It is important because they contain a lot of parameters and
    would be otherwise tedious to set up and tweak.
--- !u!4 &144885870 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400054, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &176022762
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 176022763}
  m_Layer: 0
  m_Name: MP-40
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &176022763
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 176022762}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000998378, y: -0.0000003278256, z: 0.00000019371512, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1431077418}
  m_Father: {fileID: 927881678}
  m_LocalEulerAnglesHint: {x: 0.0001, y: 0, z: 0}
--- !u!1 &192236518
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 192236519}
  - component: {fileID: 192236522}
  - component: {fileID: 192236521}
  - component: {fileID: 192236520}
  m_Layer: 9
  m_Name: Bip002 Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &192236519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192236518}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999994, y: 0.49999985, z: 0.49999955, w: -0.5000008}
  m_LocalPosition: {x: -0.0000009612739, y: 1.5410333, z: 0.026152713}
  m_LocalScale: {x: 1.0000002, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 0.0001, y: -90, z: -89.9999}
--- !u!153 &192236520
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192236518}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1849356509}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.3409621, y: 0.042164776, z: 0.00000010058284}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &192236521
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192236518}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.12842475
  m_Height: 0.31453225
  m_Direction: 0
  m_Center: {x: -0.11868882, y: 0.022110121, z: -0.0000000378514}
--- !u!54 &192236522
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192236518}
  serializedVersion: 5
  m_Mass: 5.4900002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &211851932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 211851936}
  - component: {fileID: 211851935}
  - component: {fileID: 211851934}
  - component: {fileID: 211851933}
  - component: {fileID: 211851937}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &211851933
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 211851932}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 035a48a539312914f8762cf7100ec0cb, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &211851934
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 6534570, guid: 6985759f6ce524241b3f29f410a12bab,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 211851932}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &211851935
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 211851932}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &211851936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 211851932}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.5, z: 0}
  m_LocalScale: {x: 100, y: 1, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &211851937
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 211851932}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &238743689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 238743690}
  - component: {fileID: 238743692}
  - component: {fileID: 238743691}
  m_Layer: 8
  m_Name: AimIK Before Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &238743690
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238743689}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500725621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &238743691
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238743689}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: Only bones that used as muscle targets by PuppetMaster should ba added to
    AimIK's "Bones".
--- !u!114 &238743692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238743689}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 0.634416, y: -1.5911729, z: 1.686202}
    IKPositionWeight: 1
    root: {fileID: 238743690}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 144885870}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.10531482, y: -0.00008385659, z: -2.3258327e-10}
      defaultLocalRotation: {x: -8.096637e-15, y: 0.000000010320556, z: -0.0037209948,
        w: 0.9999931}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 1382699606}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -1.6141798, y: 1.9858077, z: 2.0722418}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &238915577
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 238915578}
  - component: {fileID: 238915579}
  - component: {fileID: 238915581}
  - component: {fileID: 238915580}
  m_Layer: 9
  m_Name: Bip002 R UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &238915578
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238915577}
  serializedVersion: 2
  m_LocalRotation: {x: 0.3438806, y: 0.84495854, z: -0.2932684, w: -0.28598058}
  m_LocalPosition: {x: 0.20798887, y: 1.4053063, z: -0.01270129}
  m_LocalScale: {x: 1.0000004, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 17.3923, y: -134.12689, z: 51.697197}
--- !u!54 &238915579
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238915577}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &238915580
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238915577}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1849356509}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.20601894, y: 0.00066985196, z: -0.20798959}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &238915581
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 238915577}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.072909795
  m_Height: 0.26733592
  m_Direction: 0
  m_Center: {x: -0.12151631, y: 0, z: 0.000000031664968}
--- !u!1 &250524619
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 250524620}
  - component: {fileID: 250524621}
  - component: {fileID: 250524623}
  - component: {fileID: 250524622}
  m_Layer: 9
  m_Name: Bip002 R Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &250524620
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 250524619}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5451827, y: -0.4512536, z: -0.5518523, w: -0.44114065}
  m_LocalPosition: {x: 0.10372872, y: 0.47393277, z: 0.018549873}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -0.97669995, y: 90.0979, z: 101.7452}
--- !u!54 &250524621
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 250524619}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &250524622
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 250524619}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 803599245}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.42230687, y: 0.000000059604645, z: 0.000000091269634}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -14.729191
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 125.27081
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &250524623
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 250524619}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07556982
  m_Height: 0.4520881
  m_Direction: 0
  m_Center: {x: -0.2054947, y: 0.000000011175871, z: -0.0000000029597629}
--- !u!4 &271610268 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400024, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &283473577 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400034, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &301581032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 301581033}
  - component: {fileID: 301581035}
  - component: {fileID: 301581034}
  m_Layer: 0
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &301581033
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301581032}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: 0.5, z: -0.5000001, w: 0.49999994}
  m_LocalPosition: {x: 0, y: -5.5, z: 0}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1382699606}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &301581034
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301581032}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &301581035
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301581032}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &327833581 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400036, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &383808132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 383808133}
  - component: {fileID: 383808136}
  - component: {fileID: 383808135}
  - component: {fileID: 383808134}
  m_Layer: 9
  m_Name: Bip002 L Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &383808133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 383808132}
  serializedVersion: 2
  m_LocalRotation: {x: -0.47550592, y: -0.47550654, z: -0.52334905, w: -0.5233483}
  m_LocalPosition: {x: -0.110442206, y: 0.071606696, z: -0.065121785}
  m_LocalScale: {x: 1.0000002, y: 1.0000001, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -0.0001, y: 84.515594, z: 90}
--- !u!153 &383808134
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 383808132}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1181563547}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.4109891, y: 0, z: 0.000000014901165}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!65 &383808135
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 383808132}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.103302844, y: 0.2727195, z: 0.103302844}
  m_Center: {x: -0.019955432, y: 0.08264227, z: -0.00000002514571}
--- !u!54 &383808136
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 383808132}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &404965218 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5572294579708353132, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a0b21ec855a7db42ab30d4665de8769, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &480511552 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400008, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!850595691 &488497883
LightingSettings:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Settings.lighting
  serializedVersion: 9
  m_EnableBakedLightmaps: 1
  m_EnableRealtimeLightmaps: 1
  m_RealtimeEnvironmentLighting: 1
  m_BounceScale: 1
  m_AlbedoBoost: 1
  m_IndirectOutputScale: 1
  m_UsingShadowmask: 0
  m_BakeBackend: 1
  m_LightmapMaxSize: 1024
  m_LightmapSizeFixed: 0
  m_UseMipmapLimits: 1
  m_BakeResolution: 40
  m_Padding: 2
  m_LightmapCompression: 3
  m_AO: 0
  m_AOMaxDistance: 1
  m_CompAOExponent: 0
  m_CompAOExponentDirect: 0
  m_ExtractAO: 0
  m_MixedBakeMode: 1
  m_LightmapsBakeMode: 1
  m_FilterMode: 1
  m_LightmapParameters: {fileID: 15204, guid: 0000000000000000f000000000000000, type: 0}
  m_ExportTrainingData: 0
  m_EnableWorkerProcessBaking: 1
  m_TrainingDataDestination: TrainingData
  m_RealtimeResolution: 2
  m_ForceWhiteAlbedo: 0
  m_ForceUpdates: 0
  m_PVRCulling: 1
  m_PVRSampling: 1
  m_PVRDirectSampleCount: 32
  m_PVRSampleCount: 512
  m_PVREnvironmentSampleCount: 512
  m_PVREnvironmentReferencePointCount: 2048
  m_LightProbeSampleCountMultiplier: 4
  m_PVRBounces: 2
  m_PVRMinBounces: 2
  m_PVREnvironmentImportanceSampling: 0
  m_PVRFilteringMode: 0
  m_PVRDenoiserTypeDirect: 0
  m_PVRDenoiserTypeIndirect: 0
  m_PVRDenoiserTypeAO: 0
  m_PVRFilterTypeDirect: 0
  m_PVRFilterTypeIndirect: 0
  m_PVRFilterTypeAO: 0
  m_PVRFilteringGaussRadiusDirect: 1
  m_PVRFilteringGaussRadiusIndirect: 5
  m_PVRFilteringGaussRadiusAO: 2
  m_PVRFilteringAtrousPositionSigmaDirect: 0.5
  m_PVRFilteringAtrousPositionSigmaIndirect: 2
  m_PVRFilteringAtrousPositionSigmaAO: 1
  m_RespectSceneVisibilityWhenBakingGI: 0
--- !u!4 &500725621 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &558991727 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400022, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &560502867
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 560502868}
  - component: {fileID: 560502871}
  - component: {fileID: 560502870}
  - component: {fileID: 560502869}
  m_Layer: 9
  m_Name: Bip002 L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &560502868
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560502867}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7288649, y: 0.57203496, z: -0.3383385, w: -0.16449672}
  m_LocalPosition: {x: -0.42021608, y: 0.99223435, z: -0.04566379}
  m_LocalScale: {x: 1.0000001, y: 0.99999976, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 8.469999, y: -136.4557, z: 107.1364}
--- !u!153 &560502869
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560502867}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1819930732}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 1, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.24303265, y: -0.000000029802322, z: -0.000000022351742}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &560502870
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560502867}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.06835294
  m_Height: 0.20050195
  m_Direction: 0
  m_Center: {x: -0.09113745, y: 0.000000043469278, z: 0.00000012750633}
--- !u!54 &560502871
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560502867}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &668839409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 668839410}
  - component: {fileID: 668839411}
  m_Layer: 8
  m_Name: AimIK After Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &668839410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 668839409}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500725621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &668839411
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 668839409}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 0.634416, y: -1.5911729, z: 1.686202}
    IKPositionWeight: 1
    root: {fileID: 668839410}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 1413290004}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.10531482, y: -0.00008385468, z: -2.3258327e-10}
      defaultLocalRotation: {x: -1.1736887e-13, y: 0.000000010320554, z: -0.0037209955,
        w: 0.9999931}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 271610268}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.10534644, y: -0.00010401631, z: -2.884772e-10}
      defaultLocalRotation: {x: 4.448862e-14, y: 0.0000004163574, z: -0.15011443,
        w: 0.9886686}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 1382699606}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -1.6141798, y: 1.9858077, z: 2.0722418}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &672571030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 672571031}
  m_Layer: 0
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &672571031
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 672571030}
  serializedVersion: 2
  m_LocalRotation: {x: 0.010994822, y: -0.80512255, z: 0.5152578, w: -0.29354087}
  m_LocalPosition: {x: -0.077693544, y: -0.043539908, z: 0.0965936}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1431077418}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &679370141
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 679370142}
  - component: {fileID: 679370143}
  m_Layer: 9
  m_Name: PuppetMaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &679370142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 679370141}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1025469673}
  - {fileID: 1038530933}
  - {fileID: 1181563546}
  - {fileID: 383808133}
  - {fileID: 803599244}
  - {fileID: 250524620}
  - {fileID: 1298376663}
  - {fileID: 1849356508}
  - {fileID: 192236519}
  - {fileID: 1767199603}
  - {fileID: 1819930731}
  - {fileID: 560502868}
  - {fileID: 238915578}
  - {fileID: 697665425}
  - {fileID: 98107573}
  m_Father: {fileID: 2027615250}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &679370143
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 679370141}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  humanoidConfig: {fileID: 0}
  targetRoot: {fileID: 500725621}
  state: 0
  stateSettings:
    killDuration: 1
    deadMuscleWeight: 0.01
    deadMuscleDamper: 2
    maxFreezeSqrVelocity: 0.02
    freezePermanently: 0
    enableAngularLimitsOnKill: 1
    enableInternalCollisionsOnKill: 1
  mode: 0
  blendTime: 0.1
  fixTargetTransforms: 1
  solverIterationCount: 6
  visualizeTargetPose: 1
  mappingWeight: 1
  pinWeight: 1
  muscleWeight: 1
  muscleSpring: 200
  muscleDamper: 20
  pinPow: 4
  pinDistanceFalloff: 5
  angularPinning: 0
  updateJointAnchors: 1
  supportTranslationAnimation: 1
  angularLimits: 0
  internalCollisions: 0
  muscles:
  - name: Bip002 Pelvis
    joint: {fileID: 1025469676}
    target: {fileID: 1102897324}
    props:
      group: 0
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 0
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 L Thigh
    joint: {fileID: 1038530935}
    target: {fileID: 1095701561}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 1
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 L Calf
    joint: {fileID: 1181563548}
    target: {fileID: 1880363368}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 2
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 L Foot
    joint: {fileID: 383808134}
    target: {fileID: 480511552}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 3
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 R Thigh
    joint: {fileID: 803599246}
    target: {fileID: 1649528803}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 4
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 R Calf
    joint: {fileID: 250524622}
    target: {fileID: 19836680}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 5
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 R Foot
    joint: {fileID: 1298376664}
    target: {fileID: 283473577}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 6
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 Spine2
    joint: {fileID: 1849356511}
    target: {fileID: 144885870}
    props:
      group: 1
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 7
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 Head
    joint: {fileID: 192236520}
    target: {fileID: 686520164}
    props:
      group: 2
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 8
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 L UpperArm
    joint: {fileID: 1767199605}
    target: {fileID: 558991727}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 9
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 L Forearm
    joint: {fileID: 1819930733}
    target: {fileID: 867927796}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 10
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 L Hand
    joint: {fileID: 560502869}
    target: {fileID: 1521815036}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 11
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 R UpperArm
    joint: {fileID: 238915580}
    target: {fileID: 1251958519}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 12
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 R Forearm
    joint: {fileID: 697665427}
    target: {fileID: 327833581}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 13
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: Bip002 R Hand
    joint: {fileID: 98107574}
    target: {fileID: 927881678}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 14
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  propMuscles: []
  solvers: []
  mapDisconnectedMuscles: 1
  storeTargetMappedState: 1
--- !u!4 &686520164 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400000, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &697665424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 697665425}
  - component: {fileID: 697665426}
  - component: {fileID: 697665428}
  - component: {fileID: 697665427}
  m_Layer: 9
  m_Name: Bip002 R Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &697665425
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 697665424}
  serializedVersion: 2
  m_LocalRotation: {x: 0.57216173, y: 0.7105208, z: -0.3630726, w: -0.18965645}
  m_LocalPosition: {x: 0.3537897, y: 1.223307, z: -0.08113563}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 17.3923, y: -134.12689, z: 85.0935}
--- !u!54 &697665426
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 697665424}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &697665427
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 697665424}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 238915579}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.24303271, y: -0.000000022351738, z: 0.000000029802322}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -33.39633
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 106.60367
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &697665428
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 697665424}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.06561883
  m_Height: 0.26733592
  m_Direction: 0
  m_Center: {x: -0.12151644, y: 0.00000004470348, z: 0.000000044703487}
--- !u!1 &803599243
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 803599244}
  - component: {fileID: 803599245}
  - component: {fileID: 803599247}
  - component: {fileID: 803599246}
  m_Layer: 9
  m_Name: Bip002 R Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &803599244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 803599243}
  serializedVersion: 2
  m_LocalRotation: {x: -0.48284227, y: -0.5174142, z: -0.49075305, w: -0.50823957}
  m_LocalPosition: {x: 0.096502244, y: 0.89560556, z: -0.0034219066}
  m_LocalScale: {x: 0.99999976, y: 1, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -0.97669995, y: -269.9021, z: 87.016}
--- !u!54 &803599245
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 803599243}
  serializedVersion: 5
  m_Mass: 7.410001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &803599246
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 803599243}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1025469674}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.00000011920929, y: -0.00000009389464, z: -0.09650224}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &803599247
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 803599243}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.087358125
  m_Height: 0.46453738
  m_Direction: 0
  m_Center: {x: -0.2111534, y: -8.301413e-10, z: -0.0000000055879354}
--- !u!4 &867927796 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400010, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &885536017 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1db305b54b5c3934dbcbddbda5d14f5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &899196375
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 899196380}
  - component: {fileID: 899196379}
  - component: {fileID: 899196377}
  - component: {fileID: 899196376}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &899196376
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 899196375}
  m_Enabled: 1
--- !u!124 &899196377
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 899196375}
  m_Enabled: 1
--- !u!20 &899196379
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 899196375}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.121568635, g: 0.1254902, b: 0.121568635, a: 0.019607844}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &899196380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 899196375}
  serializedVersion: 2
  m_LocalRotation: {x: 0.041383483, y: -0.9067644, z: 0.091642536, w: 0.4094722}
  m_LocalPosition: {x: 1.963, y: 1.35, z: 2.248}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &927881678 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400038, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1025469672
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1025469673}
  - component: {fileID: 1025469674}
  - component: {fileID: 1025469676}
  - component: {fileID: 1025469675}
  m_Layer: 9
  m_Name: Bip002 Pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1025469673
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1025469672}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5000009, y: 0.5000009, z: 0.49999908, w: -0.4999991}
  m_LocalPosition: {x: -1.4901162e-10, y: 0.89560544, z: -0.0034221578}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 0, y: 269.9998, z: -90}
--- !u!54 &1025469674
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1025469672}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &1025469675
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1025469672}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.11512307
  m_Height: 0.39764768
  m_Direction: 0
  m_Center: {x: -0.122975826, y: -0.0034716763, z: 0.00000017505816}
--- !u!153 &1025469676
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1025469672}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -1.4901162e-10, y: 0.89560544, z: -0.0034221578}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1038530932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1038530933}
  - component: {fileID: 1038530934}
  - component: {fileID: 1038530936}
  - component: {fileID: 1038530935}
  m_Layer: 9
  m_Name: Bip002 L Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1038530933
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038530932}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49075022, y: -0.5082401, z: -0.48284304, w: -0.5174155}
  m_LocalPosition: {x: -0.096502244, y: 0.89560527, z: -0.003422328}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 0.9765, y: 89.9019, z: 87.0158}
--- !u!54 &1038530934
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038530932}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1038530935
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038530932}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1025469674}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.00000017881393, y: 0.00000017515256, z: 0.09650225}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1038530936
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038530932}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08735814
  m_Height: 0.46453738
  m_Direction: 0
  m_Center: {x: -0.21115339, y: 0.0000000031509444, z: 4.656613e-10}
--- !u!4 &1095701561 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400014, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1102897324 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400026, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1103343784 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100118, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1103343785
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1103343784}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72d2da3335a61af4e8bcd13bf326628c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 679370143}
  aimIKBeforePhysics: {fileID: 238743692}
  target: {fileID: 1691601609}
  fixAiming: 1
  fixLeftHand: 1
  aimIKAfterPhysics: {fileID: 668839411}
  leftHandIK: {fileID: 1373620977}
  leftHandTarget: {fileID: 672571031}
--- !u!1 &1181563545
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1181563546}
  - component: {fileID: 1181563547}
  - component: {fileID: 1181563549}
  - component: {fileID: 1181563548}
  m_Layer: 9
  m_Name: Bip002 L Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1181563546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181563545}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5518506, y: -0.44114023, z: -0.5451848, w: -0.45125347}
  m_LocalPosition: {x: -0.10372758, y: 0.4739324, z: 0.018551124}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 0.9765, y: 89.9019, z: 101.7452}
--- !u!54 &1181563547
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181563545}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1181563548
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181563545}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1038530934}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.42230675, y: 0, z: -0.000000020489098}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -14.729498
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 125.2705
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1181563549
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181563545}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075569816
  m_Height: 0.45208806
  m_Direction: 0
  m_Center: {x: -0.20549461, y: -1.7347233e-18, z: 0.0000000033425267}
--- !u!114 &1191676825 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96268f2884c13d24eb4c6351a3388a7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1191676826 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c977cd4b40934cb887c285ed1f8476e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &1251958519 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400048, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1296264477 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8691dd0ae36145d88c17f855c2eb91ba, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1296264479 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 88226de0bc8347118b6651808977535d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1298376662
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1298376663}
  - component: {fileID: 1298376666}
  - component: {fileID: 1298376665}
  - component: {fileID: 1298376664}
  m_Layer: 9
  m_Name: Bip002 R Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1298376663
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1298376662}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5233473, y: -0.52334803, z: -0.4755077, w: -0.475507}
  m_LocalPosition: {x: 0.11044488, y: 0.07160705, z: -0.06512262}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -0.0001, y: 95.4842, z: 90}
--- !u!153 &1298376664
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1298376662}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 250524621}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.41098928, y: 0.000000059604638, z: 0.000000014901163}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!65 &1298376665
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1298376662}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.10330288, y: 0.27271962, z: 0.10330288}
  m_Center: {x: -0.019955568, y: 0.0826423, z: -0.00000002188608}
--- !u!54 &1298376666
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1298376662}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &1373620975
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1373620976}
  - component: {fileID: 1373620977}
  m_Layer: 8
  m_Name: LimbIK Left Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1373620976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1373620975}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500725621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1373620977
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1373620975}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4db3c450680fd4c809d5ad90a2f24e5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -0.4202161, y: 0.9922344, z: -0.045663774}
    IKPositionWeight: 1
    root: {fileID: 1373620976}
    target: {fileID: 0}
    IKRotationWeight: 1
    IKRotation: {x: 0.72886485, y: 0.57203496, z: -0.3383385, w: -0.16449678}
    bendNormal: {x: -0.02226959, y: 0.009717905, z: 0.021600684}
    bone1:
      transform: {fileID: 558991727}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.17969485, y: 9.536744e-10, z: 0}
      defaultLocalRotation: {x: 0.3957287, y: -0.43531162, z: 0.03300276, w: 0.8079687}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone2:
      transform: {fileID: 867927796}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.2430326, y: 0.00000006103516, z: 0}
      defaultLocalRotation: {x: -0.000000030864356, y: 0.000000021855683, z: 0.28733003,
        w: 0.9578316}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone3:
      transform: {fileID: 1521815036}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.2430326, y: -0.00000003051758, z: 0}
      defaultLocalRotation: {x: -0.7212754, y: -0.08105717, z: 0.07682164, w: 0.6835861}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    goal: 2
    bendModifier: 0
    maintainRotationWeight: 0
    bendModifierWeight: 1
    bendGoal: {fileID: 0}
--- !u!1 &1382699605
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1382699606}
  m_Layer: 0
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1382699606
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382699605}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0.000000038123602, z: -0.00000006117263, w: 0.70710677}
  m_LocalPosition: {x: -0.00041241458, y: 0.06287573, z: -0.11712145}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1578922571}
  - {fileID: 301581033}
  m_Father: {fileID: 1431077418}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!4 &1413290004 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400056, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1431077417
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1431077418}
  - component: {fileID: 1431077420}
  - component: {fileID: 1431077419}
  m_Layer: 0
  m_Name: FBXExport_Props:MP40
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1431077418
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431077417}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5028256, y: -0.38515222, z: -0.5242003, w: 0.56924355}
  m_LocalPosition: {x: -0.22425295, y: 0.0017703858, z: 0.083951846}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1382699606}
  - {fileID: 672571031}
  m_Father: {fileID: 176022763}
  m_LocalEulerAnglesHint: {x: 9.7102995, y: -78.432594, z: -93.2126}
--- !u!23 &1431077419
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431077417}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: eb554719301d24a27a03d90e35702330, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1431077420
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431077417}
  m_Mesh: {fileID: 4300000, guid: 7a30e2e74b2c24169a1b8eb71cf56878, type: 3}
--- !u!4 &1521815036 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400012, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1528366055
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2027615250}
    m_Modifications:
    - target: {fileID: 100118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_Layer
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9500000, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: 862215ce0634945408572dc04fc065cd, type: 2}
    - target: {fileID: 9500000, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_UpdateMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9500000, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_CullingMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
      propertyPath: m_UpdateWhenOffscreen
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 238743690}
    - targetCorrespondingSourceObject: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 668839410}
    - targetCorrespondingSourceObject: {fileID: 400118, guid: 959d14bf134f54a6e993fe6ca6e063c0,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1373620976}
    - targetCorrespondingSourceObject: {fileID: 400038, guid: 959d14bf134f54a6e993fe6ca6e063c0,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 176022763}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 100118, guid: 959d14bf134f54a6e993fe6ca6e063c0,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1103343785}
  m_SourcePrefab: {fileID: 100100000, guid: 959d14bf134f54a6e993fe6ca6e063c0, type: 3}
--- !u!1 &1540736801
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1540736803}
  - component: {fileID: 1540736802}
  m_Layer: 0
  m_Name: _READ ME!
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1540736802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1540736801}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: 'Set PuppetMaster''s "Pin Weight" to 0 and move the "Aim Target" around to
    see the ragdoll keep aiming towards it.


    Take a look at the RagdollAiming.cs
    script on the "Dummy" gameobject.'
--- !u!4 &1540736803
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1540736801}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.14182591, y: 0.5446595, z: -0.031029701}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1578922570
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1578922571}
  - component: {fileID: 1578922573}
  - component: {fileID: 1578922572}
  m_Layer: 0
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1578922571
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1578922570}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071069, y: 0, z: 0, w: 0.70710677}
  m_LocalPosition: {x: 0, y: -5.5, z: 0}
  m_LocalScale: {x: 0.001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1382699606}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &1578922572
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1578922570}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1578922573
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1578922570}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1649528803 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400040, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1691601606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1691601609}
  - component: {fileID: 1691601608}
  - component: {fileID: 1691601607}
  m_Layer: 0
  m_Name: Aim Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &1691601607
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691601606}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aae886dd0d5d59844b4ec40cc2d96918, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1691601608
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691601606}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1691601609
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691601606}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.07337323, y: 1.332, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1767199602
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1767199603}
  - component: {fileID: 1767199604}
  - component: {fileID: 1767199606}
  - component: {fileID: 1767199605}
  m_Layer: 9
  m_Name: Bip002 L UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1767199603
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767199602}
  serializedVersion: 2
  m_LocalRotation: {x: 0.29326957, y: 0.28598356, z: -0.34388092, w: -0.84495693}
  m_LocalPosition: {x: -0.20799029, y: 1.4053059, z: -0.012702124}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -17.3922, y: -45.8736, z: 51.6974}
--- !u!54 &1767199604
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767199602}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1767199605
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767199602}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1849356509}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.20601875, y: 0.00066865986, z: 0.20798957}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1767199606
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767199602}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0729098
  m_Height: 0.26733595
  m_Direction: 0
  m_Center: {x: -0.1215163, y: 0.00000010058284, z: 0.00000006146729}
--- !u!1 &1819930730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1819930731}
  - component: {fileID: 1819930732}
  - component: {fileID: 1819930734}
  - component: {fileID: 1819930733}
  m_Layer: 9
  m_Name: Bip002 L Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1819930731
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819930730}
  serializedVersion: 2
  m_LocalRotation: {x: 0.36307457, y: 0.18965894, z: -0.5721615, w: -0.7105192}
  m_LocalPosition: {x: -0.35379016, y: 1.2233061, z: -0.081137285}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: -17.3923, y: -45.873497, z: 85.0937}
--- !u!54 &1819930732
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819930730}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &1819930733
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819930730}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1767199604}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.24303265, y: 0.0000000745058, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -33.39637
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 106.60363
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &1819930734
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819930730}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.06561881
  m_Height: 0.26733592
  m_Direction: 0
  m_Center: {x: -0.12151644, y: 0.000000040978193, z: -0.000000029594684}
--- !u!1 &1849356507
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1849356508}
  - component: {fileID: 1849356509}
  - component: {fileID: 1849356511}
  - component: {fileID: 1849356510}
  m_Layer: 9
  m_Name: Bip002 Spine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1849356508
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1849356507}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49509782, y: 0.5048543, z: 0.49509737, w: -0.5048553}
  m_LocalPosition: {x: -0.00000040853396, y: 1.1993135, z: -0.009350734}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679370142}
  m_LocalEulerAnglesHint: {x: 0.0001, y: -90, z: -88.8819}
--- !u!54 &1849356509
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1849356507}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &1849356510
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1849356507}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.1508802
  m_Height: 0.41597924
  m_Direction: 2
  m_Center: {x: -0.15857752, y: 0.0040708487, z: 0.00000003976335}
--- !u!153 &1849356511
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1849356507}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1025469674}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.30370808, y: -0.005928576, z: 0.00000042049214}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1873010496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1873010498}
  - component: {fileID: 1873010497}
  - component: {fileID: 1873010499}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1873010497
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873010496}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1873010498
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873010496}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821794, y: -0.23456973, z: 0.109381676, w: 0.87542605}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1873010499
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873010496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!4 &1880363368 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400002, guid: 959d14bf134f54a6e993fe6ca6e063c0,
    type: 3}
  m_PrefabInstance: {fileID: 1528366055}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1971722722 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5562475231938505990, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c08dd31e7f694fdcaf3d250569c31167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &1990252622
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 151253809231129524, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.000025577374
      objectReference: {fileID: 0}
    - target: {fileID: 151253809231129524, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.24901588
      objectReference: {fileID: 0}
    - target: {fileID: 151253809231129524, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.00000015893623
      objectReference: {fileID: 0}
    - target: {fileID: 701300999452479702, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"<SubModules>k__BackingField.{0es}","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 404965218}
    - target: {fileID: 1312742321894096562, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1354757304456323966, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.281422
      objectReference: {fileID: 0}
    - target: {fileID: 1354757304456323966, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.0000009000944
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"_modules.[0]","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[1]'
      value: '"path":"_modules.[1]","value":$eref:1'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[2]'
      value: '"path":"_modules.[2]","value":$eref:2'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[3]'
      value: '"path":"_modules.[3]","value":$eref:3'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[4]'
      value: '"path":"_modules.[4]","value":$eref:4'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[5]'
      value: '"path":"_modules.[5]","value":$eref:5'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[6]'
      value: '"path":"_modules.[6]","value":$eref:6'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[7]'
      value: '"path":"_modules.[7]","value":$eref:7'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 92037584}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[1]'
      value: 
      objectReference: {fileID: 885536017}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[2]'
      value: 
      objectReference: {fileID: 1971722722}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[3]'
      value: 
      objectReference: {fileID: 1296264479}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[4]'
      value: 
      objectReference: {fileID: 1296264477}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[5]'
      value: 
      objectReference: {fileID: 1191676825}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[6]'
      value: 
      objectReference: {fileID: 1191676826}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[7]'
      value: 
      objectReference: {fileID: 52640610}
    - target: {fileID: 1683269460289467898, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1783658145224123576, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2528320946427295909, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.14616035
      objectReference: {fileID: 0}
    - target: {fileID: 2641471771616559337, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.000117247924
      objectReference: {fileID: 0}
    - target: {fileID: 2641471771616559337, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.52527595
      objectReference: {fileID: 0}
    - target: {fileID: 2641471771616559337, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.0003132825
      objectReference: {fileID: 0}
    - target: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 2939897042593437468, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.097410284
      objectReference: {fileID: 0}
    - target: {fileID: 3146388501999888161, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.00011016629
      objectReference: {fileID: 0}
    - target: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 3587060510133478845, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.14535677
      objectReference: {fileID: 0}
    - target: {fileID: 3674661703824157641, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.50241
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.4333
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4214021402500490547, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.00026976602
      objectReference: {fileID: 0}
    - target: {fileID: 4214021402500490547, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.28133485
      objectReference: {fileID: 0}
    - target: {fileID: 4214021402500490547, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.00000010360966
      objectReference: {fileID: 0}
    - target: {fileID: 5126652035915794677, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.00008228863
      objectReference: {fileID: 0}
    - target: {fileID: 5126652035915794677, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.24876356
      objectReference: {fileID: 0}
    - target: {fileID: 5126652035915794677, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.000000034562557
      objectReference: {fileID: 0}
    - target: {fileID: 5596790557875754930, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5615533298249553943, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Name
      value: Female1New
      objectReference: {fileID: 0}
    - target: {fileID: 5954690637389537017, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 5954690637389537017, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 6393905998216405920, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.0013045225
      objectReference: {fileID: 0}
    - target: {fileID: 6393905998216405920, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.000018496268
      objectReference: {fileID: 0}
    - target: {fileID: 6411998766675819667, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.000027319707
      objectReference: {fileID: 0}
    - target: {fileID: 6591187809807469422, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 6591187809807469422, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 6603019320750421929, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.0000030994415
      objectReference: {fileID: 0}
    - target: {fileID: 6603019320750421929, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.033797413
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"<SubModules>k__BackingField.{0es}","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[1]'
      value: '"path":"<SubModules>k__BackingField.{1es}","value":$eref:1'
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 2023907273}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[1]'
      value: 
      objectReference: {fileID: 5918553}
    - target: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7443338930424432770, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.09741154
      objectReference: {fileID: 0}
    - target: {fileID: 7892479749728525563, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.0013654158
      objectReference: {fileID: 0}
    - target: {fileID: 7892479749728525563, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.50981885
      objectReference: {fileID: 0}
    - target: {fileID: 7892479749728525563, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.000010344475
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8199526439275725846, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8451639263143781937, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.000056363628
      objectReference: {fileID: 0}
    - target: {fileID: 8451639263143781937, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.000326395
      objectReference: {fileID: 0}
    - target: {fileID: 8830726950650311852, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8830726950650311852, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8950689789528778046, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8950689789528778046, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 9195335255939506981, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 9195335255939506981, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 73df3382d6e5d1a4a9e6a91c8157ab90, type: 3}
--- !u!114 &2023907273 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 969609528223833965, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 1990252622}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ad99c1b3efc4acb90269d1b9eb71c1e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2027615249
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2027615250}
  m_Layer: 0
  m_Name: Dummy Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2027615250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027615249}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 124257274}
  - {fileID: 679370142}
  - {fileID: 500725621}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 899196380}
  - {fileID: 1873010498}
  - {fileID: 211851936}
  - {fileID: 1540736803}
  - {fileID: 1691601609}
  - {fileID: 2027615250}
  - {fileID: 1990252622}
