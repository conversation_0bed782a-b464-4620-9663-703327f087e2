using System;
using System.Collections.Generic;
using Animancer;
using Animancer.Units;
using Events;
using Module.Mono.Animancer.RealsticFemale;
using PlayerFAP._Mono;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Events;

public sealed class LocomotionState : CharacterState
{
    [Serializable]
    public struct MovementMixers
    {
        [SerializeField] public MixerTransition2DAsset.UnShared StartWalkingBlendTree;
        [SerializeField] public MixerTransition2DAsset.UnShared WalkingBlendTree;
    }

    [Serializable]
    public struct WeaponMovementMixers
    {
        [SerializeField]
        public WeaponSubModuleState subModuleState;
        [SerializeField]
        public MixerTransition2DAsset.UnShared blendTree;
    }

    [SerializeField] private float rotationSpeed = 10f;

    [SerializeField] private float vertical;
    [SerializeField] private float horizontal;
    [SerializeField] private float verticalDump =.1f;
    [SerializeField] private float horizontalDump =.1f;

    [SerializeField] private float inputAngle;
    [SerializeField] private float inputMagnitude;
    [SerializeField] private float inputMagnitudeDamp = 0.1f;
    [SerializeField] private float InputAngleDump;


    [SerializeField] private float InputAngleDumpA = 3;
    [SerializeField] private float InputAngleDumpB = 0.8f;
    [SerializeField] private float InputAngleDumpT = 0.8f;

    [Header("Debug")]
    [SerializeField] private Vector2 m_debugParameterVector;

    public bool m_isMoving;

    [SerializeField] private float m_fadeTime;
    /************************************************************************************************************************/
    [SerializeField] private RuntimeAnimatorController m_NormalMovementAnimator;
    [SerializeField] private MovementMixers m_normalWalkingBlendTree;
    [SerializeField] private MovementMixers m_strufWalkingBlendTree;
    [SerializeField] private List<WeaponMovementMixers> m_weaponMovementMixers;

    private Vector3? m_aimTarget;
    [SerializeField] private Transform m_characterTransform;
    [SerializeField, MetersPerSecond] private float _QuickTurnMoveSpeed = 2;
    [SerializeField, Degrees] private float _QuickTurnAngle = 145;
    [SerializeField] private bool _canUpdate;
    private AnimatedFloat _FootFall;

    [SerializeField] private Float1Controller m_float1Controller;
    [SerializeField] private FootstepEventsAnimation m_footstepEventsAnimation;
    public override bool CanEnterState => Character.Parameters.IsGrounded;
    
    // Advanced animation parameters
    [Header("Animation Tuning")]
    [SerializeField] private float normalMovementResponseTime = 0.12f;
    [SerializeField] private float aimingMovementResponseTime = 0.08f;
    [SerializeField] private float startMovementBlendTime = 0.15f;
    [SerializeField] private float directionChangeBlendTime = 0.2f;
    [SerializeField] private float magnitudeChangeBlendTime = 0.1f;
    [SerializeField] private AnimationCurve directionChangeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    // Footstep timing improvement
    [SerializeField] private float footstepSpeedInfluence = 0.2f;

    // Last frame values for interpolation
    private float lastInputAngle;
    private float lastInputMagnitude;
    private float lastHorizontal;
    private float lastVertical;

    private void Awake()
    {
        m_normalWalkingBlendTree.StartWalkingBlendTree.Events.OnEnd =
            () => { _canUpdate = true; };

        m_strufWalkingBlendTree.StartWalkingBlendTree.Events.OnEnd =
            () => { _canUpdate = true; };

        m_float1Controller = new Float1Controller();
        m_float1Controller.ZeroClampThreshold = 0.01f; // Adjust this threshold based on your needs
        m_float1Controller.DampingTime = 0.1f; // Adjust the damping time as needed
        m_float1Controller.UseLinearInterpolation = false; // Start with SmoothDamp and switch to true for Lerp if needed
    }

    private void OnEnable()
    {
        EventManager.Subscribe<OnAimingOnTargetEvent>(OnDetectTarget);
        EventManager.Subscribe<OnUnAimingTargetEvent>(OnLostTarget);
    }

    private void OnDisable()
    {
        EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnDetectTarget);
        EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnLostTarget);
    }

    private void OnDetectTarget(OnAimingOnTargetEvent onAimingOnTarget)
    {
        m_aimTarget = onAimingOnTarget.TargetPosition;
    }

    private void OnLostTarget(OnUnAimingTargetEvent onLostTargetEvent)
    {
        m_aimTarget = null;
    }

    public StateMachine<CharacterState> StateMachine { get; }
    public override void OnEnterState(float fadeTime,System.Object conditions = null)
    {
        m_fadeTime = fadeTime;
        _canUpdate = false;
        if (!Character.Parameters.IsAiming)
        {
            // Initialize the input values
            InputAngleDump = InputAngleDumpA;
            inputMagnitude = Character.Parameters.InputMagnitude.Value;

            //Character.Animancer.runtimeAnimatorController = m_NormalMovementAnimator;
            //Character.Animancer.Animator.runtimeAnimatorController = m_NormalMovementAnimator;

            // Character.Animancer.Animator.SetFloat("WalkStartAngle", Character.Parameters.StartWalkAngle.Value);
            // Character.Animancer.Animator.SetFloat("InputAngle", Character.Parameters.InputAngle.Value);
            // Character.Animancer.Animator.SetFloat("InputMagnitude", Character.Parameters.InputMagnitude.Value, 0.12f,
            //     Time.deltaTime);

            Character.Animancer.Layers[0].StartFade(1, .1f);
            Character.Animancer.Play(m_normalWalkingBlendTree.StartWalkingBlendTree, fadeTime);
            m_debugParameterVector = new Vector2(Character.Parameters.StartWalkAngle.Value,
                Character.Parameters.InputMagnitude.Value);
             m_normalWalkingBlendTree.StartWalkingBlendTree.State.Parameter =
                 new Vector2(Character.Parameters.StartWalkAngle.Value, Character.Parameters.InputMagnitude.Value);
        }
        else
        {
            Character.Animancer.Layers[0].StartFade(1, .1f);
            Character.Animancer.Play(m_strufWalkingBlendTree.StartWalkingBlendTree, fadeTime);

            m_debugParameterVector = new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
            m_strufWalkingBlendTree.StartWalkingBlendTree.State.Parameter = new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
        }

    }

    public override void UpdateState()
    {
        m_isMoving = true;

        // Calculate the change in direction from last frame
        float angleDifference = 0;
        if (!Character.Parameters.IsAiming)
        {
            angleDifference = Mathf.Abs(Mathf.DeltaAngle(lastInputAngle, Character.Parameters.InputAngle.Value));
        }
        else
        {
            // For strafing, calculate the vector change
            Vector2 lastInput = new Vector2(lastHorizontal, lastVertical);
            Vector2 currentInput = new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
            angleDifference = Vector2.Angle(lastInput, currentInput);
        }

        // Adjust blend times based on direction change magnitude
        float directionBlendFactor = directionChangeCurve.Evaluate(Mathf.Clamp01(angleDifference / 180f));
        float currentDirectionBlendTime = Mathf.Lerp(normalMovementResponseTime, directionChangeBlendTime, directionBlendFactor);

        // Initial animation setup during transition
        if (!_canUpdate)
        {
            if (!Character.Parameters.IsAiming)
            {
                inputAngle = Mathf.Lerp(inputAngle, Character.Parameters.InputAngle.Value, Time.deltaTime / currentDirectionBlendTime);
                inputMagnitude = Mathf.Lerp(inputMagnitude, Character.Parameters.InputMagnitude.Value, Time.deltaTime / magnitudeChangeBlendTime);
            }
            return;
        }

        // Update animation parameters based on movement type
        if (!Character.Parameters.IsAiming)
        {
            // Normal movement (non-aiming)
            Character.Animancer.Layers[0].StartFade(1, startMovementBlendTime);
            Character.Animancer.Play(m_normalWalkingBlendTree.WalkingBlendTree);

            // Calculate adaptive response times based on movement changes
            float speedDifference = Mathf.Abs(lastInputMagnitude - Character.Parameters.InputMagnitude.Value);
            float magnitudeBlendTime = Mathf.Lerp(inputMagnitudeDamp, magnitudeChangeBlendTime, speedDifference);

            // Apply smoothing with adaptive response times
            InputAngleDump = Mathf.Lerp(InputAngleDumpA, InputAngleDumpB, Time.deltaTime / InputAngleDumpT);
            inputAngle = Mathf.Lerp(inputAngle, Character.Parameters.InputAngle.Value, Time.deltaTime / currentDirectionBlendTime);
            inputMagnitude = Mathf.Lerp(inputMagnitude, Character.Parameters.InputMagnitude.Value, Time.deltaTime / magnitudeBlendTime);

            // Update animation parameters
            m_debugParameterVector = new Vector2(inputAngle, inputMagnitude);
            m_normalWalkingBlendTree.WalkingBlendTree.State.Parameter = m_debugParameterVector;

            // Store values for next frame
            lastInputAngle = inputAngle;
            lastInputMagnitude = inputMagnitude;
        }
        else
        {
            // Aiming movement (strafing)
            Character.Animancer.Play(m_strufWalkingBlendTree.WalkingBlendTree, m_fadeTime);

            // Calculate adaptive response times for strafing
            Vector2 currentInput = new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
            Vector2 lastInput = new Vector2(horizontal, vertical);
            float inputChangeMagnitude = Vector2.Distance(lastInput, currentInput);

            // Adjust response times based on input change
            float horizontalResponseTime = Mathf.Lerp(horizontalDump, aimingMovementResponseTime, inputChangeMagnitude);
            float verticalResponseTime = Mathf.Lerp(verticalDump, aimingMovementResponseTime, inputChangeMagnitude);

            // Apply smoothing with adaptive response times
            horizontal = Mathf.Lerp(horizontal, Character.Parameters.Horizontal.Value, Time.deltaTime / horizontalResponseTime);
            vertical = Mathf.Lerp(vertical, Character.Parameters.Vertical.Value, Time.deltaTime / verticalResponseTime);

            // Update animation parameters
            m_debugParameterVector = new Vector2(horizontal, vertical);
            m_strufWalkingBlendTree.WalkingBlendTree.State.Parameter = m_debugParameterVector;

            // Debug logs for strafing/aiming movement
            //Debug.Log($"[AIMING] Character: {Character.name} | Horizontal: {Character.Parameters.Horizontal.Value}, Vertical: {Character.Parameters.Vertical.Value}, m_debugParameterVector: {m_debugParameterVector}, Transform Forward: {Character.transform.forward}, Transform Right: {Character.transform.right}");

            // Rotate character towards target
            RotateTowardsTarget();

            // Store values for next frame
            lastHorizontal = horizontal;
            lastVertical = vertical;
        }

        // Update footstep timing based on movement speed
        if (m_footstepEventsAnimation != null)
        {
            float speedFactor = Character.Parameters.InputMagnitude.Value;
            m_footstepEventsAnimation.SetSpeedMultiplier(1f + (speedFactor * footstepSpeedInfluence));
        }
    }

    private void RotateTowardsInputDirection()
    {
        if (m_debugParameterVector == Vector2.zero) return;

        Vector3 directionToMove = new Vector3(m_debugParameterVector.x, 0, m_debugParameterVector.y).normalized;
        if (directionToMove == Vector3.zero) return;

        Quaternion targetRotation = Quaternion.LookRotation(directionToMove);
        Character.Animancer.transform.rotation = Quaternion.Slerp(Character.Animancer.transform.rotation, targetRotation, Time.deltaTime * 10f); // Adjust the rotation speed as needed
    }

    private void RotateTowardsTarget()
    {
        if (m_aimTarget == null) return;

        Vector3 directionToTarget = (m_aimTarget - Character.Animancer.transform.position).Value.normalized;
        directionToTarget.y = 0; // Keep the rotation on the horizontal plane

        if (directionToTarget == Vector3.zero) return;

        Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);

        Character.Animancer.transform.rotation = Quaternion.Slerp(Character.Animancer.transform.rotation, targetRotation, Time.deltaTime * rotationSpeed); // Adjust the rotation speed as needed
    }

    public override void OnExitState()
    {
        base.OnExitState();
        m_isMoving = false;
        _canUpdate = false;
    }

    /************************************************************************************************************************/

    private void UpdateRotation()
    {
        // If the default locomotion state is not active we must be performing a quick turn.
        // Those animations use root motion to perform the turn so we don't want any scripted rotation during them.
    }

    /************************************************************************************************************************/

    [SerializeField] private UnityEvent _PlayFootstepAudio; // See the Read Me.

    private bool _CanPlayAudio;
    private bool _IsPlayingAudio;

    // This is the same logic used for locomotion audio in the original PlayerController.
    private void UpdateAudio()
    {
        const float Threshold = 0.01f;

        var footFallCurve = _FootFall.Value;
        if (footFallCurve > Threshold && !_IsPlayingAudio && _CanPlayAudio)
        {
            _IsPlayingAudio = true;
            _CanPlayAudio = false;

            _PlayFootstepAudio.Invoke();
        }
        else if (_IsPlayingAudio)
        {
            _IsPlayingAudio = false;
        }
        else if (footFallCurve < Threshold && !_CanPlayAudio)
        {
            _CanPlayAudio = true;
        }
    }

    public class Float1Controller
    {
        private float currentValue;
        private float targetValue;
        private float currentVelocity;

        public float Parameter
        {
            get => currentValue;
            set => targetValue = value;
        }

        public float DampingTime { get; set; }
        public float ZeroClampThreshold { get; set; } = 0.01f; // Threshold for clamping to zero
        public bool UseLinearInterpolation { get; set; } = false; // Option to use linear interpolation

        public Float1Controller(float initialValue = 0, float dampingTime = 0.1f)
        {
            currentValue = initialValue;
            targetValue = initialValue;
            DampingTime = dampingTime;
        }

        public void Update(float deltaTime)
        {
            if (UseLinearInterpolation)
            {
                currentValue = Mathf.Lerp(currentValue, targetValue, deltaTime / DampingTime);
            }
            else
            {
                currentValue = Mathf.SmoothDamp(currentValue, targetValue, ref currentVelocity, DampingTime, Mathf.Infinity, deltaTime);
            }

            // Clamp to zero if within the threshold
            if (Mathf.Abs(currentValue) < ZeroClampThreshold)
            {
                currentValue = 0f;
                currentVelocity = 0f;
            }
        }
    }

}
