%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 923aa56140f53bf4194f7ac9d7160c0b, type: 3}
  m_Name: AimingConfiguration
  m_EditorClassIdentifier: 
  coneAngleBeforeAiming: 120
  coneAngleAfterAiming: 270
  coneRange: 10
  detectionRadius: 7
  allowNonFOVTargets: 0
  baseDistanceThreshold: 10
  baseAngleThreshold: 45
  distanceWeight: 0.8
  angleWeight: 0.3
  targetSwitchHysteresis: 0.25
  TargetSwitchDistanceThreshold: 0.2
  MinTargetSwitchDistance: 0.2
  TargetSwitchAngleThreshold: 15
  TargetSwitchScoreThreshold: 10
  dynamicFOVExpansion: 15
  stickyAngle: 10
  stickyDistance: 2.5
  aimAssistSpeed: 120
  targetInterpolationSpeed: 0.1
  CooldownAfterLostAllEnenies: 2
  showTargetDebug: 1
