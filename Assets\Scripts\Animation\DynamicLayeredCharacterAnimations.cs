// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using Animancer;
using Animancer.Examples;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public sealed class DynamicLayeredCharacterAnimations : MonoBehaviour
    {
        /************************************************************************************************************************/

        [SerializeField] private LayeredAnimationManager _AnimationManager;
        [SerializeField] private ClipTransition m_currentUpperBodyAnimation;

        public void PlayUpperBodyAnimation(ClipTransition animation)
        {
            _AnimationManager.PlayAction(animation);
            m_currentUpperBodyAnimation = animation;
        }
        
        public void FadeOutUpperBody()
        {
            _AnimationManager.FadeOutUpperBody();
            m_currentUpperBodyAnimation = null;
        }
    }
}
