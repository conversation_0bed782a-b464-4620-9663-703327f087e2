using System;
using System.Collections.Generic;
using UnityEngine;
using Animation.Core;

namespace Animation.Core
{
    /// <summary>
    /// Represents a transition request
    /// </summary>
    [Serializable]
    public class TransitionRequest
    {
        public AnimationStateContext sourceContext;
        public AnimationStateContext targetContext;
        public TransitionPriority priority;
        public float customDuration = -1f;
        public bool forceTransition = false;
        public string reason = "";
        
        // Runtime data
        public float requestTime;
        public float startTime;
        public float duration;
        public float completionTime;
        public float progress;
        public bool IsCompleted { get; set; }
        
        public override string ToString()
        {
            return $"TransitionRequest: {sourceContext.movementState} → {targetContext.movementState} " +
                   $"({priority}, {duration:F2}s) - {reason}";
        }
    }
    
    /// <summary>
    /// Represents a transition rule
    /// </summary>
    [Serializable]
    public class TransitionRule
    {
        public string fromState;
        public string toState;
        public List<string> requiredConditions = new List<string>();
        public TransitionPriority priority = TransitionPriority.Medium;
        public bool canInterrupt = true;
        public float customDuration = -1f;
        
        public override string ToString()
        {
            return $"TransitionRule: {fromState} → {toState} ({priority}, Interrupt: {canInterrupt})";
        }
    }
    
    /// <summary>
    /// Represents a transition history entry
    /// </summary>
    [Serializable]
    public class TransitionHistory
    {
        public TransitionRequest request;
        public bool success;
        public string errorMessage;
        public float timestamp;
        
        public override string ToString()
        {
            return $"TransitionHistory: {request} - {(success ? "SUCCESS" : "FAILED")} " +
                   $"({errorMessage}) at {timestamp:F2}";
        }
    }
    
    /// <summary>
    /// Transition performance statistics
    /// </summary>
    [Serializable]
    public struct TransitionStatistics
    {
        public int totalTransitions;
        public int failedTransitions;
        public float successRate;
        public float averageDuration;
        
        public override string ToString()
        {
            return $"TransitionStats: {totalTransitions} total, {failedTransitions} failed, " +
                   $"{successRate:P1} success rate, {averageDuration:F2}s avg duration";
        }
    }
}
