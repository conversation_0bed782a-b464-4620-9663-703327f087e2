using Unity.Entities;
using Unity.Mathematics;

namespace GPUAnimationCrowds
{
    // Component to track the character's movement state
    public struct CharacterMovementState : IComponentData
    {
        // Current velocity magnitude
        public float Speed;
        
        // Whether the character is currently moving
        public bool IsMoving;
        
        // Whether the character is currently attacking
        public bool IsAttacking;
        public bool IsHit;
        
        // The time when the current attack started
        public float AttackStartTime;

        // Current movement direction
        public float3 Direction;
        
        public int CurrentAnimationID;
    }
}
