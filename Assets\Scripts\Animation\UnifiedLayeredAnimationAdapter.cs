using UnityEngine;
using Animan<PERSON>;
using Animation.Core;

namespace Module.Mono.Animancer.RealsticFemale
{
    /// <summary>
    /// Adapter class that provides the same interface as DynamicLayeredCharacterAnimations
    /// but routes calls through the UnifiedAnimationController for centralized management
    /// </summary>
    public class UnifiedLayeredAnimationAdapter : MonoBehaviour
    {
        [Header("Animation Controller Reference")]
        [SerializeField] private UnifiedAnimationController unifiedAnimationController;
        
        [<PERSON><PERSON>("Current Animation Tracking")]
        [SerializeField] private ClipTransition currentUpperBodyAnimation;
        
        private void Awake()
        {
            // Auto-find UnifiedAnimationController if not assigned
            if (unifiedAnimationController == null)
            {
                unifiedAnimationController = GetComponent<UnifiedAnimationController>();
                
                if (unifiedAnimationController == null)
                {
                    unifiedAnimationController = FindObjectOfType<UnifiedAnimationController>();
                }
                
                if (unifiedAnimationController == null)
                {
                    Debug.LogError("[UnifiedLayeredAnimationAdapter] UnifiedAnimationController not found!");
                }
            }
        }
        
        /// <summary>
        /// Play upper body animation - maintains compatibility with existing WeaponAnimationModule
        /// </summary>
        /// <param name="animation">Animation to play on upper body</param>
        public void PlayUpperBodyAnimation(ClipTransition animation)
        {
            if (unifiedAnimationController == null)
            {
                Debug.LogWarning("[UnifiedLayeredAnimationAdapter] UnifiedAnimationController not available");
                return;
            }
            
            unifiedAnimationController.PlayUpperBodyAnimation(animation);
            currentUpperBodyAnimation = animation;
            
            Debug.Log($"[UnifiedLayeredAnimationAdapter] Playing upper body animation: {animation?.Clip.name}");
        }
        
        /// <summary>
        /// Fade out upper body animation - maintains compatibility with existing system
        /// </summary>
        public void FadeOutUpperBody()
        {
            if (unifiedAnimationController == null)
            {
                Debug.LogWarning("[UnifiedLayeredAnimationAdapter] UnifiedAnimationController not available");
                return;
            }
            
            unifiedAnimationController.FadeOutUpperBody();
            currentUpperBodyAnimation = null;
            
            Debug.Log("[UnifiedLayeredAnimationAdapter] Fading out upper body animation");
        }
        
        /// <summary>
        /// Get current upper body animation
        /// </summary>
        public ClipTransition GetCurrentUpperBodyAnimation()
        {
            return currentUpperBodyAnimation;
        }
        
        /// <summary>
        /// Check if upper body animation is playing
        /// </summary>
        public bool IsUpperBodyAnimationPlaying()
        {
            return unifiedAnimationController != null && unifiedAnimationController.IsUpperBodyAnimationPlaying();
        }
        
        /// <summary>
        /// Get upper body animation weight
        /// </summary>
        public float GetUpperBodyWeight()
        {
            return unifiedAnimationController != null ? unifiedAnimationController.GetUpperBodyWeight() : 0f;
        }
        
        /// <summary>
        /// Play lower body animation (for movement) - new method for complete control
        /// </summary>
        /// <param name="animation">Animation to play on lower body</param>
        /// <param name="fadeDuration">Fade duration override</param>
        public void PlayLowerBodyAnimation(ITransition animation, float fadeDuration = -1f)
        {
            if (unifiedAnimationController == null)
            {
                Debug.LogWarning("[UnifiedLayeredAnimationAdapter] UnifiedAnimationController not available");
                return;
            }
            
            unifiedAnimationController.PlayLowerBodyAnimation(animation, fadeDuration);
            
            Debug.Log($"[UnifiedLayeredAnimationAdapter] Playing lower body animation: {animation?.GetType().Name}");
        }
    }
}
