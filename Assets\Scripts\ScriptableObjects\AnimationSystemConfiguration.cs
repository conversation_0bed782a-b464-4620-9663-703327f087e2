using System;
using System.Collections.Generic;
using UnityEngine;
using Module.Mono.Animancer.RealsticFemale;
using Animation.Core;

namespace PlayerFAP.ScriptableObjects
{
    /// <summary>
    /// Main animation system configuration that centralizes all animation settings
    /// </summary>
    [CreateAssetMenu(fileName = "AnimationSystemConfiguration", menuName = "PlayerFAP/Configurations/Animation System Configuration")]
    public class AnimationSystemConfiguration : ScriptableObject
    {
        [Header("System Settings")]
        [Tooltip("Whether to use Animan<PERSON> by default or Animator Controller")]
        public bool useAnimancerByDefault = true;
        
        [Tooltip("Enable debug logging for animation system")]
        public bool enableDebugLogging = false;
        
        [Tooltip("Default transition duration for animations")]
        [Range(0.05f, 2f)]
        public float defaultTransitionDuration = 0.25f;
        
        [Tooltip("Animation update rate in FPS")]
        [Range(15f, 60f)]
        public float updateRate = 30f;
        
        [Header("Transition Configurations")]
        [Tooltip("Transition settings for different priority levels")]
        public TransitionConfiguration transitionConfig;
        
        [Header("State Mappings")]
        [Tooltip("Movement state animation mappings")]
        public List<MovementStateMapping> movementStateMappings = new List<MovementStateMapping>();
        
        [Tooltip("Weapon state animation mappings")]
        public List<WeaponStateMapping> weaponStateMappings = new List<WeaponStateMapping>();
        
        [Header("Performance Settings")]
        [Tooltip("Performance optimization settings")]
        public PerformanceConfiguration performanceConfig;
        
        [Header("LOD Settings")]
        [Tooltip("Level of Detail settings for animations")]
        public LODConfiguration lodConfig;
        
        [Header("Debug Settings")]
        [Tooltip("Debug and visualization settings")]
        public DebugConfiguration debugConfig;
        
        #region Validation
        
        private void OnValidate()
        {
            // Ensure update rate is reasonable
            updateRate = Mathf.Clamp(updateRate, 15f, 60f);
            
            // Ensure default transition duration is reasonable
            defaultTransitionDuration = Mathf.Clamp(defaultTransitionDuration, 0.05f, 2f);
            
            // Initialize configurations if null
            if (transitionConfig == null)
                transitionConfig = new TransitionConfiguration();
            
            if (performanceConfig == null)
                performanceConfig = new PerformanceConfiguration();
            
            if (lodConfig == null)
                lodConfig = new LODConfiguration();
            
            if (debugConfig == null)
                debugConfig = new DebugConfiguration();
        }
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Get transition duration based on priority
        /// </summary>
        public float GetTransitionDuration(TransitionPriority priority)
        {
            return transitionConfig.GetDuration(priority, defaultTransitionDuration);
        }
        
        /// <summary>
        /// Get animation clip for movement state
        /// </summary>
        public AnimationClip GetMovementAnimation(MovementSubState state)
        {
            var mapping = movementStateMappings.Find(m => m.state == state);
            return mapping?.animationClip;
        }
        
        /// <summary>
        /// Get animation clip for weapon state
        /// </summary>
        public AnimationClip GetWeaponAnimation(WeaponSubState state, WeaponSubModuleState module)
        {
            var mapping = weaponStateMappings.Find(m => m.weaponState == state && m.weaponModule == module);
            return mapping?.animationClip;
        }
        
        /// <summary>
        /// Check if LOD should be applied based on distance
        /// </summary>
        public bool ShouldApplyLOD(float distance)
        {
            return lodConfig.enableLOD && distance > lodConfig.lodDistance;
        }
        
        /// <summary>
        /// Get LOD level based on distance
        /// </summary>
        public int GetLODLevel(float distance)
        {
            return lodConfig.GetLODLevel(distance);
        }
        
        #endregion
    }
    
    /// <summary>
    /// Configuration for animation transitions
    /// </summary>
    [Serializable]
    public class TransitionConfiguration
    {
        [Header("Transition Durations")]
        [Tooltip("Duration for critical priority transitions")]
        [Range(0.05f, 1f)]
        public float criticalDuration = 0.1f;
        
        [Tooltip("Duration for high priority transitions")]
        [Range(0.1f, 1f)]
        public float highDuration = 0.15f;
        
        [Tooltip("Duration for medium priority transitions")]
        [Range(0.15f, 1f)]
        public float mediumDuration = 0.25f;
        
        [Tooltip("Duration for low priority transitions")]
        [Range(0.2f, 2f)]
        public float lowDuration = 0.35f;
        
        [Header("Transition Curves")]
        [Tooltip("Easing curve for transitions")]
        public AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Tooltip("Enable smooth transition blending")]
        public bool enableSmoothBlending = true;
        
        public float GetDuration(TransitionPriority priority, float defaultDuration)
        {
            return priority switch
            {
                TransitionPriority.Critical => criticalDuration,
                TransitionPriority.High => highDuration,
                TransitionPriority.Medium => mediumDuration,
                TransitionPriority.Low => lowDuration,
                _ => defaultDuration
            };
        }
    }
    
    /// <summary>
    /// Performance optimization configuration
    /// </summary>
    [Serializable]
    public class PerformanceConfiguration
    {
        [Header("Update Optimization")]
        [Tooltip("Enable batched animation updates")]
        public bool enableBatchedUpdates = true;
        
        [Tooltip("Maximum animations to update per frame")]
        [Range(1, 50)]
        public int maxAnimationsPerFrame = 10;
        
        [Tooltip("Enable animation culling when off-screen")]
        public bool enableCulling = true;
        
        [Header("Memory Optimization")]
        [Tooltip("Enable animation state pooling")]
        public bool enableStatePooling = true;
        
        [Tooltip("Maximum pooled states")]
        [Range(10, 100)]
        public int maxPooledStates = 50;
        
        [Tooltip("Enable garbage collection optimization")]
        public bool enableGCOptimization = true;
    }
    
    /// <summary>
    /// Level of Detail configuration for animations
    /// </summary>
    [Serializable]
    public class LODConfiguration
    {
        [Header("LOD Settings")]
        [Tooltip("Enable Level of Detail system")]
        public bool enableLOD = true;
        
        [Tooltip("Distance at which LOD starts applying")]
        [Range(10f, 100f)]
        public float lodDistance = 50f;
        
        [Tooltip("LOD levels and their distance thresholds")]
        public List<LODLevel> lodLevels = new List<LODLevel>
        {
            new LODLevel { level = 0, maxDistance = 25f, updateRate = 60f },
            new LODLevel { level = 1, maxDistance = 50f, updateRate = 30f },
            new LODLevel { level = 2, maxDistance = 100f, updateRate = 15f }
        };
        
        public int GetLODLevel(float distance)
        {
            for (int i = 0; i < lodLevels.Count; i++)
            {
                if (distance <= lodLevels[i].maxDistance)
                    return lodLevels[i].level;
            }
            return lodLevels.Count > 0 ? lodLevels[lodLevels.Count - 1].level : 0;
        }
    }
    
    /// <summary>
    /// Debug and visualization configuration
    /// </summary>
    [Serializable]
    public class DebugConfiguration
    {
        [Header("Visualization")]
        [Tooltip("Show animation state gizmos in scene view")]
        public bool showAnimationGizmos = false;
        
        [Tooltip("Show transition timelines")]
        public bool showTransitionTimelines = false;
        
        [Tooltip("Show state history")]
        public bool showStateHistory = false;
        
        [Header("Logging")]
        [Tooltip("Log state transitions")]
        public bool logStateTransitions = false;
        
        [Tooltip("Log performance metrics")]
        public bool logPerformanceMetrics = false;
        
        [Tooltip("Validate transitions")]
        public bool validateTransitions = true;
        
        [Header("Colors")]
        [Tooltip("Color for movement state gizmos")]
        public Color movementStateColor = Color.blue;
        
        [Tooltip("Color for weapon state gizmos")]
        public Color weaponStateColor = Color.red;
        
        [Tooltip("Color for transition gizmos")]
        public Color transitionColor = Color.yellow;
    }
    
    /// <summary>
    /// Movement state to animation mapping
    /// </summary>
    [Serializable]
    public class MovementStateMapping
    {
        [Tooltip("Movement state")]
        public MovementSubState state;
        
        [Tooltip("Animation clip for this state")]
        public AnimationClip animationClip;
        
        [Tooltip("Custom transition duration for this state")]
        public float customTransitionDuration = -1f; // -1 means use default
        
        [Tooltip("Animation speed multiplier")]
        [Range(0.1f, 3f)]
        public float speedMultiplier = 1f;
        
        [Tooltip("Whether this animation should loop")]
        public bool isLooping = true;
    }
    
    /// <summary>
    /// Weapon state to animation mapping
    /// </summary>
    [Serializable]
    public class WeaponStateMapping
    {
        [Tooltip("Weapon state")]
        public WeaponSubState weaponState;
        
        [Tooltip("Weapon module")]
        public WeaponSubModuleState weaponModule;
        
        [Tooltip("Animation clip for this state/module combination")]
        public AnimationClip animationClip;
        
        [Tooltip("Custom transition duration")]
        public float customTransitionDuration = -1f;
        
        [Tooltip("Animation speed multiplier")]
        [Range(0.1f, 3f)]
        public float speedMultiplier = 1f;
        
        [Tooltip("Whether this animation should loop")]
        public bool isLooping = false;
    }
    
    /// <summary>
    /// LOD level definition
    /// </summary>
    [Serializable]
    public class LODLevel
    {
        [Tooltip("LOD level (0 = highest quality)")]
        public int level;
        
        [Tooltip("Maximum distance for this LOD level")]
        public float maxDistance;
        
        [Tooltip("Update rate for this LOD level")]
        public float updateRate;
    }
}
