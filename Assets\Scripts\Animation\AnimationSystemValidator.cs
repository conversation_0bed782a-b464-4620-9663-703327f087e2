using System.Collections.Generic;
using UnityEngine;
using Animation.Core;
using Module.Mono.Animancer.RealsticFemale;
using Animancer;

namespace Animation.Validation
{
    /// <summary>
    /// Validates the animation system setup and provides recommendations
    /// </summary>
    public class AnimationSystemValidator : MonoBehaviour
    {
        [Header("Validation Settings")]
        [SerializeField] private bool validateOnStart = true;
        [SerializeField] private bool showRecommendations = true;
        [SerializeField] private bool autoFixIssues = false;
        
        [System.Serializable]
        public class ValidationResult
        {
            public string component;
            public bool isValid;
            public string issue;
            public string recommendation;
            public ValidationSeverity severity;
        }
        
        public enum ValidationSeverity
        {
            Info,
            Warning,
            Error,
            Critical
        }
        
        private List<ValidationResult> validationResults = new List<ValidationResult>();
        
        private void Start()
        {
            if (validateOnStart)
            {
                ValidateAnimationSystem();
            }
        }
        
        [ContextMenu("Validate Animation System")]
        public void ValidateAnimationSystem()
        {
            validationResults.Clear();
            
            Debug.Log("[AnimationSystemValidator] Starting animation system validation...");
            
            ValidateUnifiedAnimationController();
            ValidateLayeredAnimationAdapter();
            ValidateEventDrivenManager();
            ValidateTransitionManager();
            ValidateAnimancerComponent();
            ValidateAvatarMask();
            ValidateModuleIntegration();
            
            DisplayValidationResults();
            
            if (autoFixIssues)
            {
                AttemptAutoFix();
            }
        }
        
        private void ValidateUnifiedAnimationController()
        {
            var controller = GetComponent<UnifiedAnimationController>();
            
            if (controller == null)
            {
                AddValidationResult("UnifiedAnimationController", false, 
                    "Component not found", 
                    "Add UnifiedAnimationController component to this GameObject",
                    ValidationSeverity.Critical);
                return;
            }
            
            if (!controller.IsInitialized)
            {
                AddValidationResult("UnifiedAnimationController", false,
                    "Not initialized",
                    "Ensure the controller initializes properly in Start()",
                    ValidationSeverity.Error);
            }
            else
            {
                AddValidationResult("UnifiedAnimationController", true,
                    "Properly initialized",
                    "Component is working correctly",
                    ValidationSeverity.Info);
            }
            
            // Check layered animation configuration
            if (controller.IsUpperBodyAnimationPlaying())
            {
                AddValidationResult("Layered Animations", true,
                    "Upper body animations active",
                    "Layered animation system is working",
                    ValidationSeverity.Info);
            }
            else
            {
                AddValidationResult("Layered Animations", false,
                    "No upper body animations detected",
                    "Test upper body animations to ensure layered system works",
                    ValidationSeverity.Warning);
            }
        }
        
        private void ValidateLayeredAnimationAdapter()
        {
            var adapter = GetComponent<UnifiedLayeredAnimationAdapter>();
            
            if (adapter == null)
            {
                AddValidationResult("UnifiedLayeredAnimationAdapter", false,
                    "Component not found",
                    "Add UnifiedLayeredAnimationAdapter for backward compatibility",
                    ValidationSeverity.Warning);
            }
            else
            {
                AddValidationResult("UnifiedLayeredAnimationAdapter", true,
                    "Component found",
                    "Backward compatibility adapter is available",
                    ValidationSeverity.Info);
            }
        }
        
        private void ValidateEventDrivenManager()
        {
            var eventManager = GetComponent<EventDrivenAnimationManager>();
            
            if (eventManager == null)
            {
                AddValidationResult("EventDrivenAnimationManager", false,
                    "Component not found",
                    "Add EventDrivenAnimationManager for advanced event coordination (optional)",
                    ValidationSeverity.Info);
            }
            else if (!eventManager.IsInitialized)
            {
                AddValidationResult("EventDrivenAnimationManager", false,
                    "Not initialized",
                    "Check EventManager dependencies and initialization",
                    ValidationSeverity.Warning);
            }
            else
            {
                AddValidationResult("EventDrivenAnimationManager", true,
                    "Properly initialized",
                    "Event-driven coordination is active",
                    ValidationSeverity.Info);
            }
        }
        
        private void ValidateTransitionManager()
        {
            var transitionManager = GetComponent<AnimationTransitionManager>();
            
            if (transitionManager == null)
            {
                AddValidationResult("AnimationTransitionManager", false,
                    "Component not found",
                    "Add AnimationTransitionManager for advanced transitions (optional)",
                    ValidationSeverity.Info);
            }
            else if (!transitionManager.IsInitialized)
            {
                AddValidationResult("AnimationTransitionManager", false,
                    "Not initialized",
                    "Check transition manager configuration and dependencies",
                    ValidationSeverity.Warning);
            }
            else
            {
                AddValidationResult("AnimationTransitionManager", true,
                    "Properly initialized",
                    "Advanced transition management is active",
                    ValidationSeverity.Info);
            }
        }
        
        private void ValidateAnimancerComponent()
        {
            var animancer = GetComponent<HybridAnimancerComponent>();
            
            if (animancer == null)
            {
                AddValidationResult("HybridAnimancerComponent", false,
                    "Component not found",
                    "Add HybridAnimancerComponent for Animancer support",
                    ValidationSeverity.Critical);
                return;
            }
            
            if (animancer.Layers.Count < 2)
            {
                AddValidationResult("Animancer Layers", false,
                    "Insufficient layers for layered animations",
                    "Ensure at least 2 layers are available (base + action)",
                    ValidationSeverity.Error);
            }
            else
            {
                AddValidationResult("Animancer Layers", true,
                    $"{animancer.Layers.Count} layers available",
                    "Sufficient layers for layered animations",
                    ValidationSeverity.Info);
            }
        }
        
        private void ValidateAvatarMask()
        {
            var controller = GetComponent<UnifiedAnimationController>();
            if (controller == null) return;
            
            // Use reflection to check if upperBodyMask is assigned
            var field = typeof(UnifiedAnimationController).GetField("upperBodyMask", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field != null)
            {
                var mask = field.GetValue(controller) as AvatarMask;
                if (mask == null)
                {
                    AddValidationResult("Avatar Mask", false,
                        "Upper body mask not assigned",
                        "Assign an AvatarMask for upper body animations in UnifiedAnimationController",
                        ValidationSeverity.Warning);
                }
                else
                {
                    AddValidationResult("Avatar Mask", true,
                        "Upper body mask assigned",
                        "Avatar mask is properly configured",
                        ValidationSeverity.Info);
                }
            }
        }
        
        private void ValidateModuleIntegration()
        {
            var characterModule = GetComponent<CharacterAnimationModule>();
            var weaponModule = GetComponent<WeaponAnimationModule>();
            
            if (characterModule == null)
            {
                AddValidationResult("CharacterAnimationModule", false,
                    "Component not found",
                    "Add CharacterAnimationModule for lower body animations",
                    ValidationSeverity.Error);
            }
            else
            {
                AddValidationResult("CharacterAnimationModule", true,
                    "Component found",
                    "Character animation module is available",
                    ValidationSeverity.Info);
            }
            
            if (weaponModule == null)
            {
                AddValidationResult("WeaponAnimationModule", false,
                    "Component not found",
                    "Add WeaponAnimationModule for upper body animations",
                    ValidationSeverity.Error);
            }
            else
            {
                AddValidationResult("WeaponAnimationModule", true,
                    "Component found",
                    "Weapon animation module is available",
                    ValidationSeverity.Info);
            }
        }
        
        private void AddValidationResult(string component, bool isValid, string issue, string recommendation, ValidationSeverity severity)
        {
            validationResults.Add(new ValidationResult
            {
                component = component,
                isValid = isValid,
                issue = issue,
                recommendation = recommendation,
                severity = severity
            });
        }
        
        private void DisplayValidationResults()
        {
            Debug.Log("[AnimationSystemValidator] Validation Results:");
            Debug.Log("================================================");
            
            int errors = 0, warnings = 0, infos = 0;
            
            foreach (var result in validationResults)
            {
                string icon = result.isValid ? "✅" : "❌";
                string severityIcon = result.severity switch
                {
                    ValidationSeverity.Critical => "🔴",
                    ValidationSeverity.Error => "🟠",
                    ValidationSeverity.Warning => "🟡",
                    ValidationSeverity.Info => "🔵",
                    _ => "⚪"
                };
                
                Debug.Log($"{icon} {severityIcon} {result.component}: {result.issue}");
                
                if (showRecommendations)
                {
                    UnityEngine.Debug.Log($"   💡 Recommendation: {result.recommendation}");
                }
                
                switch (result.severity)
                {
                    case ValidationSeverity.Critical:
                    case ValidationSeverity.Error:
                        errors++;
                        break;
                    case ValidationSeverity.Warning:
                        warnings++;
                        break;
                    case ValidationSeverity.Info:
                        infos++;
                        break;
                }
            }
            
            Debug.Log("================================================");
            Debug.Log($"Summary: {errors} errors, {warnings} warnings, {infos} info messages");
            
            if (errors == 0 && warnings == 0)
            {
                Debug.Log("🎉 Animation system validation passed! All systems are properly configured.");
            }
            else if (errors == 0)
            {
                Debug.Log("⚠️ Animation system has minor issues but should work correctly.");
            }
            else
            {
                Debug.Log("❌ Animation system has critical issues that need to be addressed.");
            }
        }
        
        private void AttemptAutoFix()
        {
            Debug.Log("[AnimationSystemValidator] Attempting to auto-fix issues...");
            
            foreach (var result in validationResults)
            {
                if (!result.isValid && result.severity == ValidationSeverity.Critical)
                {
                    switch (result.component)
                    {
                        case "UnifiedAnimationController":
                            if (GetComponent<UnifiedAnimationController>() == null)
                            {
                                gameObject.AddComponent<UnifiedAnimationController>();
                                Debug.Log("✅ Added UnifiedAnimationController component");
                            }
                            break;
                            
                        case "HybridAnimancerComponent":
                            if (GetComponent<HybridAnimancerComponent>() == null)
                            {
                                gameObject.AddComponent<HybridAnimancerComponent>();
                                Debug.Log("✅ Added HybridAnimancerComponent component");
                            }
                            break;
                    }
                }
            }
            
            Debug.Log("[AnimationSystemValidator] Auto-fix completed. Re-run validation to check results.");
        }
        
        public List<ValidationResult> GetValidationResults()
        {
            return new List<ValidationResult>(validationResults);
        }
    }
}
