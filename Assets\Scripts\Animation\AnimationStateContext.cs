using System;
using UnityEngine;
using Unity.Mathematics;
using Module.Mono.Animancer.RealsticFemale;

namespace Animation.Core
{
    /// <summary>
    /// Unified animation state context that consolidates all animation-related states
    /// into a single, coherent structure for improved state management and debugging.
    /// </summary>
    [Serializable]
    public struct AnimationStateContext : IEquatable<AnimationStateContext>
    {
        #region Core States
        
        [Header("Core States")]
        [SerializeField] public MainState mainState;
        [SerializeField] public MovementSubState movementState;
        [SerializeField] public WeaponSubState weaponState;
        [SerializeField] public WeaponSubModuleState weaponModuleState;
        
        #endregion
        
        #region Movement Parameters
        
        [Header("Movement Parameters")]
        [SerializeField] public float horizontal;
        [SerializeField] public float vertical;
        [SerializeField] public float inputMagnitude;
        [SerializeField] public float inputAngle;
        [SerializeField] public float startWalkAngle;
        [SerializeField] public float stopWalkAngle;
        [SerializeField] public float runningFactor;
        [SerializeField] public float3 inputDirection;
        [SerializeField] public bool isMoving;
        [SerializeField] public bool isStopping;
        [SerializeField] public bool isGrounded;
        [SerializeField] public bool isLeftFootstep;
        [SerializeField] public bool isChangeDirection;
        
        #endregion
        
        #region Aiming and Combat Parameters
        
        [Header("Aiming & Combat")]
        [SerializeField] public bool isAiming;
        [SerializeField] public bool autoAiming;
        [SerializeField] public bool detectTarget;
        [SerializeField] public bool isShooting;
        [SerializeField] public bool autoShooting;
        [SerializeField] public bool isTargetBehind;
        [SerializeField] public bool wantsToRotateBehindTarget;
        [SerializeField] public float3 aimTarget;
        
        #endregion
        
        #region Animation Timing
        
        [Header("Animation Timing")]
        [SerializeField] public float stateChangeTime;
        [SerializeField] public float lastTransitionTime;
        [SerializeField] public float transitionDuration;
        [SerializeField] public bool isTransitioning;
        
        #endregion
        
        #region Animation Layer States
        
        [Header("Animation Layers")]
        [SerializeField] public AnimationLayerState lowerBodyState;
        [SerializeField] public AnimationLayerState upperBodyState;
        [SerializeField] public float upperBodyWeight;
        [SerializeField] public float lowerBodyWeight;
        
        #endregion
        
        #region Constructors
        
        /// <summary>
        /// Creates a new AnimationStateContext with default values
        /// </summary>
        public static AnimationStateContext Default => new AnimationStateContext
        {
            mainState = MainState.Normal,
            movementState = MovementSubState.Standing,
            weaponState = WeaponSubState.Idle,
            weaponModuleState = WeaponSubModuleState.EmptyHand,
            isGrounded = true,
            upperBodyWeight = 1f,
            lowerBodyWeight = 1f,
            stateChangeTime = Time.time
        };
        
        /// <summary>
        /// Creates AnimationStateContext from current character parameters
        /// </summary>
        public static AnimationStateContext FromCharacterParameters(CharacterParameters parameters)
        {
            return new AnimationStateContext
            {
                mainState = MainState.Normal, // Would need to get from StateManager
                movementState = parameters.State,
                horizontal = parameters.Horizontal.Value,
                vertical = parameters.Vertical.Value,
                inputMagnitude = parameters.InputMagnitude.Value,
                inputAngle = parameters.InputAngle.Value,
                startWalkAngle = parameters.StartWalkAngle.Value,
                stopWalkAngle = parameters.StopWalkAngle.Value,
                runningFactor = parameters.RunningFactor.Value,
                inputDirection = parameters.PlayerInputDirection.Value,
                isAiming = parameters.IsAiming,
                autoAiming = parameters.IsAutoAiming,
                detectTarget = parameters.IsDetectTarget,
                isShooting = parameters.IsShooting,
                autoShooting = parameters.IsAutoShooting,
                isTargetBehind = parameters.IsTargetBehind,
                wantsToRotateBehindTarget = parameters.WantsToRotateBehindTarget,
                aimTarget = parameters.AimTarget,
                isStopping = parameters.IsStopping,
                isGrounded = parameters.IsGrounded,
                isLeftFootstep = parameters.IsLeftFootstep,
                isChangeDirection = parameters.IsChangeDirection,
                weaponModuleState = (WeaponSubModuleState)parameters.WeaponIndex,
                stateChangeTime = Time.time,
                upperBodyWeight = 1f,
                lowerBodyWeight = 1f
            };
        }
        
        #endregion
        
        #region State Comparison
        
        /// <summary>
        /// Checks if this context has changed compared to another context
        /// </summary>
        public bool HasChanged(AnimationStateContext other)
        {
            return !Equals(other);
        }
        
        /// <summary>
        /// Checks if movement-related states have changed
        /// </summary>
        public bool HasMovementChanged(AnimationStateContext other)
        {
            return movementState != other.movementState ||
                   !Mathf.Approximately(inputMagnitude, other.inputMagnitude) ||
                   !Mathf.Approximately(inputAngle, other.inputAngle) ||
                   isMoving != other.isMoving ||
                   isStopping != other.isStopping ||
                   isChangeDirection != other.isChangeDirection;
        }
        
        /// <summary>
        /// Checks if weapon-related states have changed
        /// </summary>
        public bool HasWeaponChanged(AnimationStateContext other)
        {
            return weaponState != other.weaponState ||
                   weaponModuleState != other.weaponModuleState ||
                   isAiming != other.isAiming ||
                   isShooting != other.isShooting;
        }
        
        /// <summary>
        /// Checks if any core states have changed (used for major transitions)
        /// </summary>
        public bool HasCoreStateChanged(AnimationStateContext other)
        {
            return mainState != other.mainState ||
                   movementState != other.movementState ||
                   weaponState != other.weaponState ||
                   weaponModuleState != other.weaponModuleState;
        }
        
        #endregion
        
        #region IEquatable Implementation
        
        public bool Equals(AnimationStateContext other)
        {
            return mainState == other.mainState &&
                   movementState == other.movementState &&
                   weaponState == other.weaponState &&
                   weaponModuleState == other.weaponModuleState &&
                   Mathf.Approximately(horizontal, other.horizontal) &&
                   Mathf.Approximately(vertical, other.vertical) &&
                   Mathf.Approximately(inputMagnitude, other.inputMagnitude) &&
                   Mathf.Approximately(inputAngle, other.inputAngle) &&
                   isAiming == other.isAiming &&
                   isShooting == other.isShooting &&
                   isMoving == other.isMoving &&
                   detectTarget == other.detectTarget;
        }
        
        public override bool Equals(object obj)
        {
            return obj is AnimationStateContext other && Equals(other);
        }
        
        public override int GetHashCode()
        {
            return HashCode.Combine(
                (int)mainState,
                (int)movementState,
                (int)weaponState,
                (int)weaponModuleState,
                isAiming,
                isShooting,
                isMoving
            );
        }
        
        public static bool operator ==(AnimationStateContext left, AnimationStateContext right)
        {
            return left.Equals(right);
        }
        
        public static bool operator !=(AnimationStateContext left, AnimationStateContext right)
        {
            return !left.Equals(right);
        }
        
        #endregion
        
        #region Debug and Serialization
        
        /// <summary>
        /// Returns a human-readable string representation of the animation state
        /// </summary>
        public override string ToString()
        {
            return $"AnimationStateContext: Main={mainState}, Movement={movementState}, " +
                   $"Weapon={weaponState}({weaponModuleState}), Aiming={isAiming}, " +
                   $"Shooting={isShooting}, Moving={isMoving}, InputMag={inputMagnitude:F2}";
        }
        
        /// <summary>
        /// Creates a detailed debug string with all parameters
        /// </summary>
        public string ToDetailedString()
        {
            return $"AnimationStateContext:\n" +
                   $"  Core: {mainState} | {movementState} | {weaponState} | {weaponModuleState}\n" +
                   $"  Movement: Mag={inputMagnitude:F2}, Angle={inputAngle:F1}, Moving={isMoving}, Stopping={isStopping}\n" +
                   $"  Combat: Aiming={isAiming}, Shooting={isShooting}, Target={detectTarget}, Behind={isTargetBehind}\n" +
                   $"  Input: H={horizontal:F2}, V={vertical:F2}, Dir={inputDirection}\n" +
                   $"  Timing: StateTime={stateChangeTime:F2}, Transitioning={isTransitioning}";
        }
        
        #endregion
    }
    
    /// <summary>
    /// Represents the state of an individual animation layer
    /// </summary>
    [Serializable]
    public struct AnimationLayerState
    {
        public string currentAnimation;
        public float normalizedTime;
        public float weight;
        public bool isPlaying;
        public float fadeTime;
        
        public static AnimationLayerState Default => new AnimationLayerState
        {
            currentAnimation = "Idle",
            normalizedTime = 0f,
            weight = 1f,
            isPlaying = true,
            fadeTime = 0.25f
        };
    }
}
