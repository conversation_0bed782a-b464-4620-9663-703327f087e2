using System;
using System.Collections.Generic;
using UnityEngine;
using Events;
using Animation.Core;
using Module.Mono.Animancer.RealsticFemale;

namespace Animation.StateSync
{
    /// <summary>
    /// Centralized state synchronization system that unifies MovementSubState, WeaponSubState,
    /// and AnimationSubState into a cohesive state representation using event-driven architecture.
    /// </summary>
    public class CentralizedStateSynchronizer : MonoBehaviour
    {
        #region Properties and Fields

        [Header("State Synchronization Configuration")]
        [SerializeField] private bool enableStateSynchronization = true;
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private float synchronizationInterval = 0.016f; // 60 FPS

        [Header("Current Unified State")]
        [SerializeField] private UnifiedAnimationState currentUnifiedState;
        [SerializeField] private UnifiedAnimationState previousUnifiedState;

        [Header("State Tracking")]
        [SerializeField] private MovementSubState currentMovementState = MovementSubState.Standing;
        [SerializeField] private WeaponSubState currentWeaponState = WeaponSubState.Idle;
        [SerializeField] private AnimationSubState currentAnimationState = AnimationSubState.Idle;
        [SerializeField] private WeaponSubModuleState currentWeaponModule = WeaponSubModuleState.EmptyHand;
        [SerializeField] private MainState currentMainState = MainState.Normal;

        [Header("State Change Tracking")]
        [SerializeField] private bool hasMovementChanged = false;
        [SerializeField] private bool hasWeaponChanged = false;
        [SerializeField] private bool hasAnimationChanged = false;
        [SerializeField] private bool hasMainStateChanged = false;

        [Header("Synchronization Statistics")]
        [SerializeField] private int totalStateChanges = 0;
        [SerializeField] private int synchronizationUpdates = 0;
        [SerializeField] private float lastSynchronizationTime = 0f;

        [Header("Target Loss Handling")]
        [SerializeField] private float targetLossCooldown = 0.5f;
        [SerializeField] private bool isInTargetLossCooldown = false;
        private float targetLossCooldownEndTime = 0f;

        // Event tracking
        private Dictionary<Type, int> eventCounts = new Dictionary<Type, int>();

        // State validation
        private StateValidationRules validationRules;

        #endregion

        #region Events

        /// <summary>
        /// Event fired when unified state changes
        /// </summary>
        public event Action<UnifiedAnimationState, UnifiedAnimationState> OnUnifiedStateChanged;

        /// <summary>
        /// Event fired when state synchronization occurs
        /// </summary>
        public event Action<StateSynchronizationInfo> OnStateSynchronized;

        /// <summary>
        /// Event fired when state validation fails
        /// </summary>
        public event Action<StateValidationError> OnStateValidationFailed;

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            // Initialize validation rules
            validationRules = new StateValidationRules();

            // Initialize unified state
            currentUnifiedState = new UnifiedAnimationState();
            previousUnifiedState = new UnifiedAnimationState();

            LogDebug("CentralizedStateSynchronizer initialized");
        }

        private void Start()
        {
            // Subscribe to all relevant state change events
            SubscribeToStateEvents();

            // Initialize with current state
            SynchronizeStates();
        }

        private void Update()
        {
            // Check target loss cooldown
            if (isInTargetLossCooldown && Time.time >= targetLossCooldownEndTime)
            {
                isInTargetLossCooldown = false;
                LogDebug("Target loss cooldown ended, resuming normal state synchronization");
            }

            if (enableStateSynchronization && Time.time - lastSynchronizationTime >= synchronizationInterval)
            {
                // Skip synchronization during target loss cooldown to prevent rapid state changes
                if (!isInTargetLossCooldown)
                {
                    SynchronizeStates();
                }
                lastSynchronizationTime = Time.time;
            }
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            UnsubscribeFromStateEvents();
        }

        #endregion

        #region Event Management

        /// <summary>
        /// Subscribe to all state change events
        /// </summary>
        private void SubscribeToStateEvents()
        {
            EventManager.Subscribe<OnMovementStateChangedEvent>(OnMovementStateChanged);
            EventManager.Subscribe<OnWeaponStateChangedEvent>(OnWeaponStateChanged);
            EventManager.Subscribe<OnAnimationStateChangedEvent>(OnAnimationStateChanged);
            EventManager.Subscribe<OnCombatModeChangedEvent>(OnCombatModeChanged);
            EventManager.Subscribe<OnAnimationChangeRequestEvent>(OnAnimationChangeRequest);

            // Target loss events to handle cooldown
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnAllLostTargetEvent>(OnAllTargetsLost);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnUnAiming);

            LogDebug("Subscribed to state change events");
        }

        /// <summary>
        /// Unsubscribe from state change events
        /// </summary>
        private void UnsubscribeFromStateEvents()
        {
            EventManager.Unsubscribe<OnMovementStateChangedEvent>(OnMovementStateChanged);
            EventManager.Unsubscribe<OnWeaponStateChangedEvent>(OnWeaponStateChanged);
            EventManager.Unsubscribe<OnAnimationStateChangedEvent>(OnAnimationStateChanged);
            EventManager.Unsubscribe<OnCombatModeChangedEvent>(OnCombatModeChanged);
            EventManager.Unsubscribe<OnAnimationChangeRequestEvent>(OnAnimationChangeRequest);

            // Target loss events
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnAllLostTargetEvent>(OnAllTargetsLost);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnUnAiming);

            LogDebug("Unsubscribed from state change events");
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle movement state changes
        /// </summary>
        private void OnMovementStateChanged(OnMovementStateChangedEvent eventData)
        {
            LogDebug($"Movement state changed: {eventData.PreviousState} → {eventData.NewState}");

            if (currentMovementState != eventData.NewState)
            {
                currentMovementState = eventData.NewState;
                hasMovementChanged = true;
                IncrementEventCount<OnMovementStateChangedEvent>();

                // Trigger immediate synchronization for critical state changes
                if (IsCriticalMovementState(eventData.NewState))
                {
                    SynchronizeStates();
                }
            }
        }

        /// <summary>
        /// Handle weapon state changes
        /// </summary>
        private void OnWeaponStateChanged(OnWeaponStateChangedEvent eventData)
        {
            LogDebug($"Weapon state changed: {eventData.PreviousState} → {eventData.NewState}");

            bool stateChanged = false;

            if (currentWeaponState != eventData.NewState)
            {
                currentWeaponState = eventData.NewState;
                hasWeaponChanged = true;
                stateChanged = true;
            }

            if (currentWeaponModule != eventData.WeaponModule)
            {
                currentWeaponModule = eventData.WeaponModule;
                hasWeaponChanged = true;
                stateChanged = true;
            }

            if (stateChanged)
            {
                IncrementEventCount<OnWeaponStateChangedEvent>();

                // Trigger immediate synchronization for weapon changes
                if (IsCriticalWeaponState(eventData.NewState))
                {
                    SynchronizeStates();
                }
            }
        }

        /// <summary>
        /// Handle animation state changes
        /// </summary>
        private void OnAnimationStateChanged(OnAnimationStateChangedEvent eventData)
        {
            LogDebug($"Animation state changed: {eventData.PreviousState} → {eventData.NewState}");

            if (currentAnimationState != eventData.NewState)
            {
                currentAnimationState = eventData.NewState;
                hasAnimationChanged = true;
                IncrementEventCount<OnAnimationStateChangedEvent>();

                // Animation state changes are usually less critical, use normal synchronization
                // unless it's a critical animation state
                if (IsCriticalAnimationState(eventData.NewState))
                {
                    SynchronizeStates();
                }
            }
        }

        /// <summary>
        /// Handle combat mode changes
        /// </summary>
        private void OnCombatModeChanged(OnCombatModeChangedEvent eventData)
        {
            LogDebug($"Combat mode changed: {eventData.MainState}");

            if (currentMainState != eventData.MainState)
            {
                currentMainState = eventData.MainState;
                hasMainStateChanged = true;
                IncrementEventCount<OnCombatModeChangedEvent>();

                // Main state changes are always critical
                SynchronizeStates();
            }
        }

        /// <summary>
        /// Handle animation change requests
        /// </summary>
        private void OnAnimationChangeRequest(OnAnimationChangeRequestEvent eventData)
        {
            LogDebug($"Animation change request: Movement={eventData.MovementState}, Weapon={eventData.WeaponState}");

            // Animation change requests can update multiple states at once
            bool stateChanged = false;

            if (currentMovementState != eventData.MovementState)
            {
                currentMovementState = eventData.MovementState;
                hasMovementChanged = true;
                stateChanged = true;
            }

            if (currentWeaponState != eventData.WeaponState)
            {
                currentWeaponState = eventData.WeaponState;
                hasWeaponChanged = true;
                stateChanged = true;
            }

            if (currentWeaponModule != eventData.WeaponModule)
            {
                currentWeaponModule = eventData.WeaponModule;
                hasWeaponChanged = true;
                stateChanged = true;
            }

            if (stateChanged)
            {
                IncrementEventCount<OnAnimationChangeRequestEvent>();

                // Force immediate synchronization for animation requests
                if (eventData.ForceTransition)
                {
                    SynchronizeStates();
                }
            }
        }

        /// <summary>
        /// Handle target lost events - start cooldown to prevent rapid state changes
        /// </summary>
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            StartTargetLossCooldown("Single target lost");
        }

        /// <summary>
        /// Handle all targets lost events - start cooldown to prevent rapid state changes
        /// </summary>
        private void OnAllTargetsLost(OnAllLostTargetEvent eventData)
        {
            StartTargetLossCooldown("All targets lost");
        }

        /// <summary>
        /// Handle un-aiming events - start cooldown to prevent rapid state changes
        /// </summary>
        private void OnUnAiming(OnUnAimingTargetEvent eventData)
        {
            StartTargetLossCooldown("Un-aiming");
        }

        /// <summary>
        /// Start target loss cooldown to prevent rapid state changes
        /// </summary>
        private void StartTargetLossCooldown(string reason)
        {
            if (!isInTargetLossCooldown)
            {
                isInTargetLossCooldown = true;
                targetLossCooldownEndTime = Time.time + targetLossCooldown;
                LogDebug($"Started target loss cooldown for {targetLossCooldown}s - Reason: {reason}");
            }
        }

        #endregion

        #region State Synchronization

        /// <summary>
        /// Synchronize all states into unified state representation
        /// </summary>
        private void SynchronizeStates()
        {
            // Store previous state for comparison
            previousUnifiedState = currentUnifiedState;

            // Create new unified state
            var newUnifiedState = new UnifiedAnimationState
            {
                mainState = currentMainState,
                movementState = currentMovementState,
                weaponState = currentWeaponState,
                animationState = currentAnimationState,
                weaponModuleState = currentWeaponModule,
                timestamp = Time.time,
                frameCount = Time.frameCount
            };

            // Validate state transition
            var validationResult = validationRules.ValidateStateTransition(currentUnifiedState, newUnifiedState);
            if (!validationResult.IsValid)
            {
                LogDebug($"State validation failed: {validationResult.ErrorMessage}");
                OnStateValidationFailed?.Invoke(new StateValidationError
                {
                    PreviousState = currentUnifiedState,
                    AttemptedState = newUnifiedState,
                    ErrorMessage = validationResult.ErrorMessage,
                    Timestamp = Time.time
                });

                // Optionally revert to previous state or apply correction
                if (validationResult.ShouldRevert)
                {
                    RevertToValidState();
                    return;
                }
            }

            // Update current unified state
            currentUnifiedState = newUnifiedState;

            // Check if state actually changed
            if (HasUnifiedStateChanged())
            {
                totalStateChanges++;
                synchronizationUpdates++;

                // Broadcast unified state change event
                OnUnifiedStateChanged?.Invoke(previousUnifiedState, currentUnifiedState);

                // Broadcast synchronization info
                var syncInfo = new StateSynchronizationInfo
                {
                    PreviousState = previousUnifiedState,
                    NewState = currentUnifiedState,
                    ChangedComponents = GetChangedComponents(),
                    SynchronizationTime = Time.time,
                    TotalChanges = totalStateChanges
                };

                OnStateSynchronized?.Invoke(syncInfo);

                // Broadcast unified state change event to the event system
                BroadcastUnifiedStateChange(previousUnifiedState, currentUnifiedState);

                LogDebug($"State synchronized: {GetStateChangeDescription()}");
            }

            // Reset change flags
            ResetChangeFlags();
        }

        /// <summary>
        /// Check if unified state has changed
        /// </summary>
        private bool HasUnifiedStateChanged()
        {
            return hasMovementChanged || hasWeaponChanged || hasAnimationChanged || hasMainStateChanged ||
                   !currentUnifiedState.Equals(previousUnifiedState);
        }

        /// <summary>
        /// Get description of state changes
        /// </summary>
        private string GetStateChangeDescription()
        {
            var changes = new List<string>();

            if (hasMainStateChanged)
                changes.Add($"Main: {previousUnifiedState.mainState} → {currentUnifiedState.mainState}");
            if (hasMovementChanged)
                changes.Add($"Movement: {previousUnifiedState.movementState} → {currentUnifiedState.movementState}");
            if (hasWeaponChanged)
                changes.Add($"Weapon: {previousUnifiedState.weaponState} → {currentUnifiedState.weaponState}");
            if (hasAnimationChanged)
                changes.Add($"Animation: {previousUnifiedState.animationState} → {currentUnifiedState.animationState}");

            return string.Join(", ", changes);
        }

        /// <summary>
        /// Get changed components for synchronization info
        /// </summary>
        private StateChangeComponents GetChangedComponents()
        {
            return new StateChangeComponents
            {
                MainStateChanged = hasMainStateChanged,
                MovementStateChanged = hasMovementChanged,
                WeaponStateChanged = hasWeaponChanged,
                AnimationStateChanged = hasAnimationChanged
            };
        }

        /// <summary>
        /// Reset change tracking flags
        /// </summary>
        private void ResetChangeFlags()
        {
            hasMovementChanged = false;
            hasWeaponChanged = false;
            hasAnimationChanged = false;
            hasMainStateChanged = false;
        }

        /// <summary>
        /// Revert to previous valid state
        /// </summary>
        private void RevertToValidState()
        {
            LogDebug("Reverting to previous valid state");

            currentUnifiedState = previousUnifiedState;

            // Restore individual states
            currentMainState = previousUnifiedState.mainState;
            currentMovementState = previousUnifiedState.movementState;
            currentWeaponState = previousUnifiedState.weaponState;
            currentAnimationState = previousUnifiedState.animationState;
            currentWeaponModule = previousUnifiedState.weaponModuleState;

            ResetChangeFlags();
        }

        #endregion

        #region State Validation

        /// <summary>
        /// Check if movement state is critical and requires immediate synchronization
        /// </summary>
        private bool IsCriticalMovementState(MovementSubState state)
        {
            return state == MovementSubState.Stop ||
                   state == MovementSubState.WalkingStart ||
                   state == MovementSubState.InPositionRotation;
        }

        /// <summary>
        /// Check if weapon state is critical and requires immediate synchronization
        /// </summary>
        private bool IsCriticalWeaponState(WeaponSubState state)
        {
            return state == WeaponSubState.Shooting ||
                   state == WeaponSubState.Equipping ||
                   state == WeaponSubState.UnEquipping;
        }

        /// <summary>
        /// Check if animation state is critical and requires immediate synchronization
        /// </summary>
        private bool IsCriticalAnimationState(AnimationSubState state)
        {
            return state == AnimationSubState.Shooting ||
                   state == AnimationSubState.TakingDamage ||
                   state == AnimationSubState.Dying;
        }

        #endregion

        #region Event Broadcasting

        /// <summary>
        /// Broadcast unified state change to event system
        /// </summary>
        private void BroadcastUnifiedStateChange(UnifiedAnimationState previous, UnifiedAnimationState current)
        {
            var unifiedStateEvent = new OnUnifiedStateChangedEvent(previous, current);
            EventManager.Broadcast(unifiedStateEvent);
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Increment event count for tracking
        /// </summary>
        private void IncrementEventCount<T>()
        {
            var eventType = typeof(T);
            if (eventCounts.ContainsKey(eventType))
                eventCounts[eventType]++;
            else
                eventCounts[eventType] = 1;
        }

        /// <summary>
        /// Log debug message if logging is enabled
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[CentralizedStateSynchronizer] {message}");
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Get current unified state
        /// </summary>
        public UnifiedAnimationState GetCurrentUnifiedState()
        {
            return currentUnifiedState;
        }

        /// <summary>
        /// Get previous unified state
        /// </summary>
        public UnifiedAnimationState GetPreviousUnifiedState()
        {
            return previousUnifiedState;
        }

        /// <summary>
        /// Force immediate state synchronization
        /// </summary>
        public void ForceSynchronization()
        {
            SynchronizeStates();
        }

        /// <summary>
        /// Get synchronization statistics
        /// </summary>
        public StateSynchronizationStats GetSynchronizationStats()
        {
            return new StateSynchronizationStats
            {
                TotalStateChanges = totalStateChanges,
                SynchronizationUpdates = synchronizationUpdates,
                EventCounts = new Dictionary<Type, int>(eventCounts),
                LastSynchronizationTime = lastSynchronizationTime,
                CurrentUnifiedState = currentUnifiedState
            };
        }

        /// <summary>
        /// Enable or disable state synchronization
        /// </summary>
        public void SetSynchronizationEnabled(bool enabled)
        {
            enableStateSynchronization = enabled;
            LogDebug($"State synchronization {(enabled ? "enabled" : "disabled")}");
        }

        /// <summary>
        /// Set synchronization interval
        /// </summary>
        public void SetSynchronizationInterval(float interval)
        {
            synchronizationInterval = Mathf.Max(0.001f, interval); // Minimum 1ms
            LogDebug($"Synchronization interval set to {synchronizationInterval:F3}s");
        }

        #endregion
    }
}