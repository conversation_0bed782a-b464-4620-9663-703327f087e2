using Unity.Entities;
using PlayerFAP.Components.Detection;
using PlayerFAP.Tags;
using UnityEngine;
using Events;
using System.Collections.Generic;

namespace PlayerFAP.Systems.Detection
{
    /// <summary>
    /// System to update detection parameters in the ECS system based on events from AimingModule
    /// Following the Event-Driven Communication principle from rules.md
    /// </summary>
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    public partial class DetectionParameterUpdateSystem : SystemBase
    {
        // Queue to store parameter update events
        private Queue<OnUpdateDetectionParametersEvent> parameterUpdateEvents = new Queue<OnUpdateDetectionParametersEvent>();

        protected override void OnCreate()
        {
            RequireForUpdate<PlayerTag>();
            RequireForUpdate<SphereDetectSensorComponent>();

            // Subscribe to detection parameter update events
            EventManager.Subscribe<OnUpdateDetectionParametersEvent>(OnUpdateDetectionParameters);
        }

        protected override void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<OnUpdateDetectionParametersEvent>(OnUpdateDetectionParameters);
        }

        private void OnUpdateDetectionParameters(OnUpdateDetectionParametersEvent evt)
        {
            // Store the event in the queue to process during OnUpdate
            parameterUpdateEvents.Enqueue(evt);
        }

        protected override void OnUpdate()
        {
            // Process any pending parameter update events
            while (parameterUpdateEvents.Count > 0)
            {
                var evt = parameterUpdateEvents.Dequeue();

                // Update all player entities with the new detection parameters
                Entities
                    .WithAll<PlayerTag>()
                    .ForEach((ref SphereDetectSensorComponent sensor) =>
                    {
                        // Update detection parameters from AimingModule (source of truth)
                        sensor.DetectionAngle = evt.DetectionAngle;
                        sensor.DetectionRange = evt.DetectionRange;
                        sensor.DetectionRadius = evt.DetectionRadius;
                    })
                    .WithoutBurst()
                    .Run();
            }
        }
    }
}
